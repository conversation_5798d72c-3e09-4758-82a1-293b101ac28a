# Please add type annotations to function definitions in this file and remove this line!
# mypy: allow-untyped-defs

from datetime import datetime, timedelta
from typing import List, Type, Union, Optional, Sequence
from zoneinfo import ZoneInfo

from api_server.services.case_labels.graphql.case_labels import CaseLabelCategory
from api_server.services.closures.closure_store import SiteClosure
from api_server.services.closures.graphql.site_closure import SiteClosureConnection
from api_server.services.closures.graphql.site_closure_loader import FrozenSiteClosureQueryDto
from api_server.services.first_case_config.dtos import SiteFirstCaseConfigDto
from api_server.services.prime_time.dtos import SitePrimeTime
from api_server.services.prime_time.graphql.site_capacity_constraint_loader import (
    FrozenSiteCapacityConstraintQueryDto,
)
import graphene

import api_server.services.organization.graphql.organization as organization_schema
import api_server.services.room.graphql.room as room_schema
from api_server.graphql.context import GrapheneInfo
from api_server.graphql.pagination.fields import PaginationConnection, pagination_connection_factory
from api_server.graphql.sorting.arguments import SortedPaginationConnectionField
from api_server.services.room.room_store import RoomModel
from api_server.services.prime_time.graphql.site_prime_time_config import SitePrimeTimeConfig
from api_server.services.first_case_config.graphql.site_first_case_config import SiteFirstCaseConfig
from api_server.services.prime_time.graphql.site_capacity_constraint import SiteCapacityConstraint
from api_server.services.prime_time.prime_time_store import (
    SiteCapacityConstraint as SiteCapacityConstraintModel,
)
from api_server.services.site.site_store import Site as SiteModel
from api_server.services.staffing_needs.graphql.staffing_needs_ratio import (
    StaffingNeedsRatio,
)
from api_server.services.room.room_service import RoomStatusNameGraphene
from api_server.services.apella_case.graphql.apella_case_loader import (
    FrozenRoomApellaCasesQueryDto,
)
from api_server.services.turnover.turnover_service import DEFAULT_MAX_TURNOVER_MINS


class TurnoverGoals(graphene.ObjectType):
    goal_minutes = graphene.Int(required=False)
    max_minutes = graphene.Int(required=True)


class Site(graphene.ObjectType):
    id = graphene.ID(required=True)

    name = graphene.String(required=True)
    timezone = graphene.String(required=True)

    organization_id = graphene.String(required=True)
    organization = graphene.Field(lambda: organization_schema.Organization, required=True)

    rooms = SortedPaginationConnectionField(
        lambda: room_schema.RoomConnection,
        room_ids=graphene.List(graphene.NonNull(graphene.String), default_value=None),
        statusFilter=graphene.List(graphene.NonNull(RoomStatusNameGraphene), default_value=None),
        required=True,
    )
    turnover_goals = graphene.Field(TurnoverGoals, required=True)

    prime_time_config = graphene.Field(lambda: SitePrimeTimeConfig, required=True)

    first_case_config = graphene.Field(lambda: SiteFirstCaseConfig, required=True)

    capacity_constraints = graphene.NonNull(graphene.List(graphene.NonNull(SiteCapacityConstraint)))

    staffing_needs_ratios = graphene.NonNull(graphene.List(graphene.NonNull(StaffingNeedsRatio)))

    closures = SortedPaginationConnectionField(
        lambda: SiteClosureConnection,
        required=False,
    )

    case_label_form = graphene.NonNull(graphene.List(graphene.NonNull(CaseLabelCategory)))

    @staticmethod
    async def resolve_organization_id(site: SiteModel, info: GrapheneInfo, **kwargs):
        return site.org_id

    @staticmethod
    async def resolve_organization(site: SiteModel, info: GrapheneInfo, **kwargs):
        return await info.context.org_loader.load(site.org_id)

    @staticmethod
    async def resolve_prime_time_config(
        site: SiteModel,
        info: GrapheneInfo,
    ) -> SitePrimeTime:
        return await info.context.site_prime_time_config_loader.load(site.id)

    @staticmethod
    async def resolve_first_case_config(
        site: SiteModel,
        info: GrapheneInfo,
    ) -> SiteFirstCaseConfigDto:
        return await info.context.site_first_case_config_loader.load(site.id)

    @staticmethod
    async def resolve_capacity_constraints(
        site: SiteModel,
        info: GrapheneInfo,
    ) -> Sequence[SiteCapacityConstraintModel]:
        return await info.context.site_capacity_constraint_loader.load(
            FrozenSiteCapacityConstraintQueryDto(site_id=site.id)
        )

    @staticmethod
    async def resolve_rooms(
        site: SiteModel,
        info: GrapheneInfo,
        room_ids: Union[List[str], None] = None,
        statusFilter: Optional[List[RoomStatusNameGraphene]] = None,
        **kwargs,
    ) -> Union[List[RoomModel], List[RoomModel]]:
        # With the room_service, to get the ids to then give to a loader is just as expensive
        # as just getting all the rooms directly. So don't bother to use a loader here.
        if room_ids is None:
            rooms: Sequence[RoomModel] = await info.context.site_rooms_loader.load(site.id)
            rooms = [room for room in rooms if room]
        else:
            rooms_with_none: List[Optional[RoomModel]] = await info.context.room_loader.load_many(
                room_ids
            )
            rooms = [room for room in rooms_with_none if room and room.site_id == site.id]
        if statusFilter:
            # The RoomModels returned above don't include status information.
            # We query the cases to calculate the status for each room and return only those rooms
            # whose status is in the specified statusFilter.
            result: List[RoomModel] = []
            for room in rooms:
                now = datetime.now().astimezone(tz=ZoneInfo(site.timezone))
                min_end_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                max_start_time = min_end_time + timedelta(days=1)
                # Query cases within the time range for the current room
                case_query = FrozenRoomApellaCasesQueryDto(
                    org_id=site.org_id,
                    site_ids=(site.id,),
                    room_id=room.id,
                    min_end_time=min_end_time,
                    max_start_time=max_start_time,
                    case_ids=None,
                    case_types=None,
                    phase_ids=None,
                    staff_ids=None,
                    case_matching_statuses=None,
                    scheduled_case_status=None,
                )
                cases_in_room = await info.context.room_apella_case_loader.load(case_query)
                turnover_goals = await info.context.turnover_goals_loader.load(site.id)
                # Calculate the status of the current room based on the retrieved cases
                status = await info.context.room_service.get_room_status(
                    list(cases_in_room), now, turnover_goals.max_minutes if turnover_goals else None
                )
                if status.name in statusFilter:
                    result.append(room)
            return result
        else:
            return rooms

    @staticmethod
    async def resolve_staffing_needs_ratios(site: SiteModel, info: GrapheneInfo, **kwargs):
        return await info.context.staffing_needs_service.get_staffing_needs_ratios(site.id)

    @staticmethod
    async def resolve_turnover_goals(site: SiteModel, info: GrapheneInfo, **kwargs):
        goals = await info.context.turnover_goals_loader.load(site.id)

        if goals is None:
            default_goals = TurnoverGoals()
            default_goals.goal_minutes = None
            default_goals.max_minutes = DEFAULT_MAX_TURNOVER_MINS
            return default_goals

        return goals

    @staticmethod
    async def resolve_closures(
        site: SiteModel,
        info: GrapheneInfo,
    ) -> Sequence[SiteClosure]:
        return await info.context.site_closure_loader.load(
            FrozenSiteClosureQueryDto(site_id=site.id)
        )

    @staticmethod
    async def resolve_case_label_form(site: SiteModel, info: GrapheneInfo):
        return await info.context.case_label_service.query_case_label_form(site.org_id, site.id)


SiteConnection: Type[PaginationConnection] = pagination_connection_factory(Site)
