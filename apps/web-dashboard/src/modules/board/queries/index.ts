import { gql } from '@apollo/client'

export const BOARD_CONFIG_FRAGMENT = gql`
  fragment BoardConfigFragment on BoardConfig {
    id
    name
    enableVideo
    site {
      id
    }
    updatedTime
    updatedByUser {
      name
    }
    rooms {
      id
    }
    blurVideo
    pageDuration
    pageSize
    boardViewType
    zoomPercent
    showClosedRooms
  }
`

export const DELETE_BOARD_CONFIG = gql`
  mutation DeleteBoardConfig($boardConfigId: ID!) {
    boardConfigDelete(input: { boardConfigId: $boardConfigId }) {
      success
    }
  }
`

export const UPDATE_BOARD_CONFIG = gql`
  ${BOARD_CONFIG_FRAGMENT}
  mutation UpdateBoardConfig(
    $name: String!
    $boardConfigId: ID!
    $blurVideo: Boolean!
    $pageDuration: Int!
    $pageSize: Int!
    $roomIds: [ID!]
    $boardViewType: BoardViewType!
    $enableVideo: Boolean!
    $zoomPercent: Int!
    $showClosedRooms: Boolean
  ) {
    boardConfigUpdate(
      input: {
        name: $name
        boardConfigId: $boardConfigId
        blurVideo: $blurVideo
        pageDuration: $pageDuration
        pageSize: $pageSize
        roomIds: $roomIds
        boardViewType: $boardViewType
        enableVideo: $enableVideo
        zoomPercent: $zoomPercent
        showClosedRooms: $showClosedRooms
      }
    ) {
      success
      boardConfig {
        ...BoardConfigFragment
      }
    }
  }
`

export const CREATE_BOARD_CONFIG = gql`
  ${BOARD_CONFIG_FRAGMENT}
  mutation CreateBoardConfig(
    $id: UUID!
    $name: String!
    $siteId: ID!
    $orgId: ID!
    $blurVideo: Boolean!
    $pageDuration: Int!
    $pageSize: Int!
    $roomIds: [ID!]
    $boardViewType: BoardViewType!
    $enableVideo: Boolean!
    $zoomPercent: Int!
    $showClosedRooms: Boolean
  ) {
    boardConfigCreate(
      input: {
        id: $id
        name: $name
        siteId: $siteId
        orgId: $orgId
        blurVideo: $blurVideo
        pageDuration: $pageDuration
        pageSize: $pageSize
        roomIds: $roomIds
        boardViewType: $boardViewType
        enableVideo: $enableVideo
        zoomPercent: $zoomPercent
        showClosedRooms: $showClosedRooms
      }
    ) {
      success
      boardConfig {
        ...BoardConfigFragment
      }
    }
  }
`

export const GET_BOARD_CONFIGS = gql`
  ${BOARD_CONFIG_FRAGMENT}
  query BoardConfigs($orgIds: [String!]) {
    boardConfigs(query: { organizationIds: $orgIds }) {
      edges {
        node {
          ...BoardConfigFragment
        }
      }
    }
  }
`

export const GET_BOARD_CONFIGS_WITH_SITES = gql`
  ${BOARD_CONFIG_FRAGMENT}
  query BoardConfigsWithSites($siteIds: [String!]) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name
        }
      }
    }
    boardConfigs(query: { siteIds: $siteIds }) {
      edges {
        node {
          ...BoardConfigFragment
        }
      }
    }
  }
`

export const GET_BIG_BOARD_DATA = gql`
  query BigBoardData(
    $siteIds: [String!]!
    $roomIds: [String!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
    $caseTypes: [CaseType!]
    $skipPatientInfo: Boolean!
    $statusFilter: [RoomStatusName!]
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          name
          id
          rooms(
            roomIds: $roomIds
            statusFilter: $statusFilter
            minEndTime: $minEndTime
            maxStartTime: $maxStartTime
            orderBy: [
              { sort: "sortKey", direction: ASC }
              { sort: "name", direction: ASC }
            ]
          ) {
            edges {
              node {
                id
                name
                status {
                  name
                  since
                  inProgressApellaCase {
                    id
                    status(useObservations: true) {
                      name
                      since
                    }
                  }
                }
                apellaCases(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                    caseTypes: $caseTypes
                  }
                  orderBy: [{ sort: "startTime", direction: ASC }]
                ) {
                  edges {
                    node {
                      id
                      type
                      startTime
                      endTime
                      status(useObservations: true) {
                        name
                        since
                      }
                      case {
                        id
                        isAddOn
                        scheduledStartTime
                        scheduledEndTime
                        caseFlags {
                          id
                          flagType
                        }
                        caseStaff {
                          role
                          staff {
                            id
                            firstName
                            lastName
                          }
                        }
                        primaryCaseProcedures {
                          anesthesia {
                            name
                          }
                          procedure {
                            name
                          }
                        }
                        caseStaffPlan(query: { includeArchived: false }) {
                          edges {
                            node {
                              id
                              archivedTime
                              role
                              case {
                                id
                              }
                              staff {
                                id
                                firstName
                                lastName
                              }
                            }
                          }
                        }
                        patient @skip(if: $skipPatientInfo) {
                          id
                          personalInfo {
                            firstNameAbbreviated
                            lastNameAbbreviated
                            age
                            administrativeSex {
                              text
                            }
                          }
                        }
                        notePlan {
                          id
                          note
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`
