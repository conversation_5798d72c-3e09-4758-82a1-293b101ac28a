import { gql } from '@apollo/client'

export const GET_TURNOVER_DATA = gql`
  query GetTurnoverData(
    $siteIds: [String!]
    $roomIds: [String!]
    $minEndTime: DateTime!
    $maxStartTime: DateTime!
    $statusFilter: [RoomStatusName!]
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          name
          turnoverGoals {
            goalMinutes
            maxMinutes
          }
          rooms(roomIds: $roomIds, statusFilter: $statusFilter, minEndTime: $minEndTime, maxStartTime: $maxStartTime) {
            edges {
              node {
                name
                id
                status {
                  name
                  inProgressApellaCase {
                    status {
                      name
                    }
                  }
                }
                turnovers(
                  query: {
                    minEndTime: $minEndTime
                    maxStartTime: $maxStartTime
                  }
                ) {
                  id
                  startTime
                  endTime
                  type
                  status {
                    name
                  }
                  precedingCase {
                    status(useObservations: true) {
                      name
                    }
                  }
                  followingCase {
                    id
                    type
                    startTime
                    endTime
                    status(useObservations: true) {
                      name
                    }
                    case {
                      id
                      scheduledStartTime
                      scheduledEndTime
                      isAddOn
                      isInFlipRoom
                      caseStaff {
                        role
                        staff {
                          id
                          firstName
                          lastName
                        }
                      }
                      precedingCase {
                        id
                      }

                      primaryCaseProcedures {
                        anesthesia {
                          name
                        }
                        procedure {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`
