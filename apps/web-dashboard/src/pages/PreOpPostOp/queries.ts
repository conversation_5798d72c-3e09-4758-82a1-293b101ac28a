import { gql } from '@apollo/client'

export const GET_PRE_POST_OP_DATA = gql`
  query GetPrePostOpData(
    $minTime: DateTime!
    $maxTime: DateTime!
    $siteIds: [String!]
    $roomIds: [String!]
    $skipPatientInfo: Boolean!
    $includeStaffPlan: Boolean!
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name
          rooms(roomIds: $roomIds) {
            edges {
              node {
                id
                name
                apellaCases(
                  query: { minEndTime: $minTime, maxStartTime: $maxTime }
                  orderBy: { sort: "startTime", direction: ASC }
                ) {
                  edges {
                    node {
                      id
                      startTime
                      endTime
                      type
                      room {
                        id
                        name
                        status {
                          name
                          since
                          inProgressApellaCase {
                            id
                            status(useObservations: true) {
                              name
                              since
                            }
                          }
                        }
                      }
                      status(useObservations: true) {
                        name
                        since
                      }
                      case {
                        id
                        site {
                          id
                        }
                        scheduledStartTime
                        scheduledEndTime
                        caseFlags(includeArchived: false)
                          @include(if: $includeStaffPlan) {
                          id
                          flagType
                          archivedTime
                        }
                        caseStaff {
                          role
                          staff {
                            id
                            firstName
                            lastName
                          }
                        }
                        primaryCaseProcedures {
                          anesthesia {
                            name
                          }
                          procedure {
                            name
                            id
                          }
                        }
                        isAddOn
                        isInFlipRoom
                        patientClass
                        isFirstCase
                        precedingCase {
                          id
                        }
                        caseClassificationType {
                          id
                          name
                        }
                        externalCaseId
                        patient @skip(if: $skipPatientInfo) {
                          id
                          personalInfo {
                            firstNameAbbreviated
                            lastNameAbbreviated
                            age
                            administrativeSex {
                              text
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`
