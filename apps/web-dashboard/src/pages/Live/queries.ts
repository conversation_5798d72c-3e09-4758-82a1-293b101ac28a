import { gql } from '@apollo/client'

import { EventSourceType } from '../types'

export const LIVE_APELLA_CASE_FRAGMENT = gql`
  fragment LiveApellaCaseFragment on ApellaCase {
    id
    startTime
    endTime
    type
    case {
      caseStaff(onlyPrimarySurgeons: true) {
        role
        staff {
          id
          firstName
          lastName
        }
      }
      primaryCaseProcedures {
        procedure {
          id
          name
        }
      }
      isAddOn
    }
  }
`

export const GET_LIVE_ROOMS = gql`
  ${LIVE_APELLA_CASE_FRAGMENT}
  query GetLiveRooms(
    $minTime: DateTime!
    $maxTime: DateTime!
    $statusFilter: [RoomStatusName!]
    $siteIds: [String!]
    $roomIds: [String!]
  ) {
    sites(siteIds: $siteIds) {
      edges {
        node {
          id
          name
          turnoverGoals {
              goalMinutes
              maxMinutes
            }
          rooms(statusFilter: $statusFilter, roomIds: $roomIds, minEndTime: $minTime, maxStartTime: $maxTime) {
            edges {
              node {
                id
                sortKey
                name
                status {
                  name
                  since
                  inProgressTurnover {
                    id
                    startTime
                    endTime
                    followingCase {
                      ...LiveApellaCaseFragment
                    }
                    type
                  }
                  inProgressApellaCase {
                    ...LiveApellaCaseFragment
                    status(useObservations: true) {
                      name
                      since
                    }
                  }
                  nextCase {
                    ...LiveApellaCaseFragment
                  }
                }
                roomEvents(
                  query: {
                    minTime: $minTime
                    maxTime: $maxTime
                    sourceTypes: ["${EventSourceType.Prediction}", "${EventSourceType.Human}"]
                    includeDashboardEventsOnly: true
                  }
                  orderBy: [{ sort: "startTime", direction: DESC }]
                ) {
                  edges {
                    node {
                      id
                      name
                      startTime
                      attrs {
                        id
                        name
                        color
                      }
                    }
                  }
                }
                apellaCases(
                  query: { minEndTime: $minTime, maxStartTime: $maxTime }
                  orderBy: [{ sort: "startTime", direction: ASC }]
                ) {
                  edges {
                    node {
                      id
                      startTime
                      endTime
                      type
                      case {
                        id
                        isFirstCase
                        isAddOn
                        caseClassificationType {
                          id
                        }
                        precedingCase {
                          id
                        }
                        scheduledStartTime
                      }
                    }
                  }
                }
                defaultCamera {
                  id
                }
                cameras {
                  edges {
                    node {
                      id
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

export const GET_LIVE_IMAGE = gql`
  query GetLiveImage($cameraId: String!) {
    camera(id: $cameraId) {
      id
      latestImage {
        id
        captureTime
        imageBytes
      }
      room {
        id
        privacyEnabled
      }
    }
  }
`

export const GET_LIVE_EVENTS = gql`
  query GetLiveEvents(
    $siteId: String!
    $minTime: DateTime!
    $maxTime: DateTime!
  ) {
    site(id: $siteId) {
      id
      rooms {
        edges {
          node {
            id
            roomEvents(
              query: {
                minTime: $minTime
                maxTime: $maxTime
                sourceTypes: ["${EventSourceType.Prediction}", "${EventSourceType.Human}"]
                includeDashboardEventsOnly: true
              }
            ) {
              edges {
                node {
                  id
                  name
                  startTime
                  attrs {
                    id
                    name
                    color
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`
