import sgqlc.types
import sgqlc.types.datetime
import sgqlc.types.relay


api_server_schema = sgqlc.types.Schema()


# Unexport Node/PageInfo, let schema re-declare them
api_server_schema -= sgqlc.types.relay.Node
api_server_schema -= sgqlc.types.relay.PageInfo


########################################################################
# Scalars and Enumerations
########################################################################
class AdministrativeSexType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("AMBIGUOUS", "FEMAL<PERSON>", "MA<PERSON>", "NOT_APPLICABLE", "OTHER", "UNKNOWN")


class BoardViewType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("TILE", "TIMELINE")


Boolean = sgqlc.types.Boolean


class CancelledReason(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = (
        "BLOCKED_CAMERAS",
        "CAMERAS_OUT_OF_SYNC",
        "IDLE",
        "OUTAGE",
        "SENSITIVE_CONTENT",
        "SKIP",
        "STAFF_IN_TRAINING",
    )


class CaseForecastStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("INVALID", "VALID")


class CaseLabelFieldType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("BOOLEAN", "SINGLE_SELECT")


class CaseMatchingStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("AUTOMATIC", "CANCELED", "NOT_A_CASE", "OVERRIDE")


class CaseSource(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("EXTERNAL", "INTERNAL")


class CaseStaffRole(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("ALL_ANESTHESIA", "ANESTHESIA", "CIRCULATOR", "PRIMARY_SURGEON", "SCRUB_TECH")


class CaseStatusName(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = (
        "ACTUAL",
        "COMPLETE",
        "IN_FACILITY",
        "IN_HOLD",
        "PHASE_II",
        "PREP",
        "PRE_PROCEDURE",
        "PRE_PROCEDURE_COMPLETE",
        "RECOVERY",
        "SCHEDULED",
        "SURGERY",
        "WRAP_UP",
    )


class CaseType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("COMPLETE", "FORECAST", "LIVE")


class CleanScoreEnum(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("COMPLETE", "MISSED", "PARTIAL")


class ContactInformationType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("PHONE_NUMBER",)


Date = sgqlc.types.datetime.Date

DateTime = sgqlc.types.datetime.DateTime


class DayOfWeek(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("FRIDAY", "MONDAY", "SATURDAY", "SUNDAY", "THURSDAY", "TUESDAY", "WEDNESDAY")


class Direction(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("ASC", "DESC")


class Duration(sgqlc.types.Scalar):
    __schema__ = api_server_schema


class EventMatchingStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("AUTOMATIC", "OVERRIDE")


class FeedbackStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("COMPLETE", "INCOMPLETE")


class FirstCaseConfigSource(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("DEFAULT", "ROOM", "SITE")


Float = sgqlc.types.Float

ID = sgqlc.types.ID

Int = sgqlc.types.Int


class JSON(sgqlc.types.Scalar):
    __schema__ = api_server_schema


class PatientClass(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = (
        "EMERGENCY",
        "HOSPITAL_OUTPATIENT_SURGERY",
        "INPATIENT",
        "OBSERVATION",
        "OTHER",
        "PRE_ADMIT",
        "SURGERY_ADMIT",
    )


class PhaseStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("INVALID", "VALID")


class PhaseType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = (
        "ANESTHESIA_PREP",
        "ANESTHESIA_PROCEDURE",
        "CASE",
        "DRAPE_DRAPE_TURNOVER",
        "INTRA_OPERATIVE",
        "POST_OPERATIVE",
        "PRE_OPERATIVE",
        "TERMINAL_CLEAN",
        "TURNOVER",
        "TURNOVER_CLEAN",
        "TURNOVER_CLEANED",
        "TURNOVER_CLEAN_V2",
        "TURNOVER_OPEN",
    )


class PrimeTimeConfigSource(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("DEFAULT", "ROOM", "SITE")


class RoomStatusName(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("CLOSED", "IDLE", "IN_CASE", "TURNOVER")


class SiteFirstCaseConfigSource(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("DEFAULT", "SITE")


class SitePrimeTimeConfigSource(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("DEFAULT", "SITE")


String = sgqlc.types.String


class TaskStatus(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = (
        "BLOCKED",
        "CANCELLED",
        "DONE",
        "IN_PROGRESS",
        "IN_REVIEW",
        "NOT_STARTED",
        "READY_FOR_REVIEW",
    )


Time = sgqlc.types.datetime.Time


class TurnoverStatusName(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("CLEANED", "CLEANING", "OPENING")


class TurnoverType(sgqlc.types.Enum):
    __schema__ = api_server_schema
    __choices__ = ("COMPLETE", "FORECAST", "LIVE")


class UUID(sgqlc.types.Scalar):
    __schema__ = api_server_schema


########################################################################
# Input Objects
########################################################################
class AnesthesiaQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id", "names")
    org_id = sgqlc.types.Field(String, graphql_name="orgId")
    names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="names"
    )


class AnesthesiaUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("name", "org_id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class AnnnotationTaskTypeCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "name",
        "description",
        "event_types",
        "context_event_types",
        "annotator_ids",
        "provisional_annotator_ids",
        "reviewer_ids",
        "priority",
        "detect_idle",
        "allow_skipping_review",
        "optimize_tasks",
        "schedules",
    )
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")
    event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="eventTypes"
    )
    context_event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="contextEventTypes"
    )
    annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="annotatorIds"
    )
    provisional_annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="provisionalAnnotatorIds"
    )
    reviewer_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="reviewerIds"
    )
    priority = sgqlc.types.Field(Int, graphql_name="priority")
    detect_idle = sgqlc.types.Field(Boolean, graphql_name="detectIdle")
    allow_skipping_review = sgqlc.types.Field(Boolean, graphql_name="allowSkippingReview")
    optimize_tasks = sgqlc.types.Field(Boolean, graphql_name="optimizeTasks")
    schedules = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("AnnotationTaskScheduleInput")),
        graphql_name="schedules",
    )


class AnnnotationTaskTypeUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "description",
        "event_types",
        "context_event_types",
        "annotator_ids",
        "provisional_annotator_ids",
        "reviewer_ids",
        "archived",
        "priority",
        "detect_idle",
        "allow_skipping_review",
        "optimize_tasks",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="eventTypes"
    )
    context_event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="contextEventTypes"
    )
    annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="annotatorIds"
    )
    provisional_annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="provisionalAnnotatorIds"
    )
    reviewer_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="reviewerIds"
    )
    archived = sgqlc.types.Field(Boolean, graphql_name="archived")
    priority = sgqlc.types.Field(Int, graphql_name="priority")
    detect_idle = sgqlc.types.Field(Boolean, graphql_name="detectIdle")
    allow_skipping_review = sgqlc.types.Field(Boolean, graphql_name="allowSkippingReview")
    optimize_tasks = sgqlc.types.Field(Boolean, graphql_name="optimizeTasks")


class AnnotationTaskBulkGenerateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_time", "end_time", "task_type_ids")
    start_time = sgqlc.types.Field(DateTime, graphql_name="startTime")
    end_time = sgqlc.types.Field(DateTime, graphql_name="endTime")
    task_type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="taskTypeIds"
    )


class AnnotationTaskBulkUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("update_input", "query_input", "task_ids")
    update_input = sgqlc.types.Field(
        sgqlc.types.non_null("AnnotationTaskBulkUpdateUpdateInput"), graphql_name="updateInput"
    )
    query_input = sgqlc.types.Field("AnnotationTaskQueryInput", graphql_name="queryInput")
    task_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="taskIds"
    )


class AnnotationTaskBulkUpdateUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("annotator_user_id", "reviewer_user_id", "status", "cancelled_reason")
    annotator_user_id = sgqlc.types.Field(ID, graphql_name="annotatorUserId")
    reviewer_user_id = sgqlc.types.Field(ID, graphql_name="reviewerUserId")
    status = sgqlc.types.Field(TaskStatus, graphql_name="status")
    cancelled_reason = sgqlc.types.Field(CancelledReason, graphql_name="cancelledReason")


class AnnotationTaskExitInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("task_id",)
    task_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="taskId")


class AnnotationTaskNextAnnotateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("current_task_id", "status")
    current_task_id = sgqlc.types.Field(ID, graphql_name="currentTaskId")
    status = sgqlc.types.Field(TaskStatus, graphql_name="status")


class AnnotationTaskNextReviewInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("current_task_id", "status")
    current_task_id = sgqlc.types.Field(ID, graphql_name="currentTaskId")
    status = sgqlc.types.Field(TaskStatus, graphql_name="status")


class AnnotationTaskQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "start_time",
        "end_time",
        "min_updated_time",
        "max_updated_time",
        "statuses",
        "annotator_user_ids",
        "reviewer_user_ids",
        "organization_id",
        "site_id",
        "room_id",
        "type_ids",
    )
    start_time = sgqlc.types.Field(DateTime, graphql_name="startTime")
    end_time = sgqlc.types.Field(DateTime, graphql_name="endTime")
    min_updated_time = sgqlc.types.Field(DateTime, graphql_name="minUpdatedTime")
    max_updated_time = sgqlc.types.Field(DateTime, graphql_name="maxUpdatedTime")
    statuses = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(TaskStatus)), graphql_name="statuses"
    )
    annotator_user_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="annotatorUserIds")
    reviewer_user_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="reviewerUserIds")
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_id = sgqlc.types.Field(ID, graphql_name="siteId")
    room_id = sgqlc.types.Field(ID, graphql_name="roomId")
    type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="typeIds"
    )


class AnnotationTaskScheduleCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("annotation_task_type_id", "start_time", "interval", "site_ids", "room_ids")
    annotation_task_type_id = sgqlc.types.Field(
        sgqlc.types.non_null(ID), graphql_name="annotationTaskTypeId"
    )
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    interval = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="interval")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )


class AnnotationTaskScheduleInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_time", "interval", "site_ids", "room_ids")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    interval = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="interval")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )


class AnnotationTaskScheduleUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "start_time", "interval", "site_ids", "room_ids")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    start_time = sgqlc.types.Field(Time, graphql_name="startTime")
    interval = sgqlc.types.Field(Int, graphql_name="interval")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )


class AnnotationTaskUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "annotator_user_id", "reviewer_user_id", "status", "cancelled_reason")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    annotator_user_id = sgqlc.types.Field(ID, graphql_name="annotatorUserId")
    reviewer_user_id = sgqlc.types.Field(ID, graphql_name="reviewerUserId")
    status = sgqlc.types.Field(TaskStatus, graphql_name="status")
    cancelled_reason = sgqlc.types.Field(CancelledReason, graphql_name="cancelledReason")


class ApellaCaseBaseQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_end_time",
        "max_start_time",
        "case_ids",
        "phase_ids",
        "staff_ids",
        "case_types",
        "case_matching_statuses",
        "scheduled_case_status",
    )
    min_end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxStartTime")
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    phase_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="phaseIds"
    )
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    case_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseType)), graphql_name="caseTypes"
    )
    case_matching_statuses = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseMatchingStatus)),
        graphql_name="caseMatchingStatuses",
    )
    scheduled_case_status = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="scheduledCaseStatus"
    )


class ApellaCaseQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_end_time",
        "max_start_time",
        "case_ids",
        "phase_ids",
        "staff_ids",
        "case_types",
        "case_matching_statuses",
        "scheduled_case_status",
        "organization_id",
        "site_ids",
        "room_ids",
    )
    min_end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxStartTime")
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    phase_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="phaseIds"
    )
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    case_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseType)), graphql_name="caseTypes"
    )
    case_matching_statuses = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseMatchingStatus)),
        graphql_name="caseMatchingStatuses",
    )
    scheduled_case_status = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="scheduledCaseStatus"
    )
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )


class AvailableTimeSlotInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id", "start_time", "end_time", "max_available_duration")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    max_available_duration = sgqlc.types.Field(
        sgqlc.types.non_null(Duration), graphql_name="maxAvailableDuration"
    )


class AvailableTimeSlotQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("min_available_duration", "site_ids", "start_date", "end_date", "surgeon_id")
    min_available_duration = sgqlc.types.Field(
        sgqlc.types.non_null(Duration), graphql_name="minAvailableDuration"
    )
    site_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(String)), graphql_name="siteIds"
    )
    start_date = sgqlc.types.Field(Date, graphql_name="startDate")
    end_date = sgqlc.types.Field(Date, graphql_name="endDate")
    surgeon_id = sgqlc.types.Field(String, graphql_name="surgeonId")


class BlockCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "color", "org_id", "site_ids", "block_times", "surgeon_ids")
    id = sgqlc.types.Field(ID, graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(String, graphql_name="color")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    block_times = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeInput")), graphql_name="blockTimes"
    )
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="surgeonIds"
    )


class BlockQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_end_time",
        "max_start_time",
        "ids",
        "names",
        "days_of_week",
        "org_ids",
        "site_ids",
        "room_ids",
        "surgeon_ids",
        "include_archived",
    )
    min_end_time = sgqlc.types.Field(DateTime, graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    ids = sgqlc.types.Field(sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="ids")
    names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="names"
    )
    days_of_week = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(Int)), graphql_name="daysOfWeek"
    )
    org_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="orgIds"
    )
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="surgeonIds"
    )
    include_archived = sgqlc.types.Field(Boolean, graphql_name="includeArchived")


class BlockReleaseProcessDateRangeInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_ids", "start_date", "end_date")
    room_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(String)), graphql_name="roomIds"
    )
    start_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="startDate")
    end_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="endDate")


class BlockReleaseReprocessInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_date", "end_date", "org_id")
    start_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="startDate")
    end_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="endDate")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class BlockTimeAvailableIntervalInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "room_ids", "min_end_time", "max_start_time")
    site_id = sgqlc.types.Field(ID, graphql_name="siteId")
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    min_end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxStartTime")


class BlockTimeBulkCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("block_times",)
    block_times = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeInput"))),
        graphql_name="blockTimes",
    )


class BlockTimeBulkDeleteDuplicateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id",)
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class BlockTimeInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "block_id", "start_time", "end_time", "room_id", "releases")
    id = sgqlc.types.Field(ID, graphql_name="id")
    block_id = sgqlc.types.Field(ID, graphql_name="blockId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    releases = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeReleaseInput")), graphql_name="releases"
    )


class BlockTimeQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "room_ids", "min_end_time", "max_start_time")
    site_id = sgqlc.types.Field(ID, graphql_name="siteId")
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    min_end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxStartTime")


class BlockTimeReleaseInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "block_time_id",
        "start_time",
        "end_time",
        "reason",
        "source",
        "to_block",
        "released_time",
        "unreleased_time",
        "unreleased_source",
        "to_block_type",
    )
    id = sgqlc.types.Field(ID, graphql_name="id")
    block_time_id = sgqlc.types.Field(ID, graphql_name="blockTimeId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    reason = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="reason")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    to_block = sgqlc.types.Field(String, graphql_name="toBlock")
    released_time = sgqlc.types.Field(DateTime, graphql_name="releasedTime")
    unreleased_time = sgqlc.types.Field(DateTime, graphql_name="unreleasedTime")
    unreleased_source = sgqlc.types.Field(String, graphql_name="unreleasedSource")
    to_block_type = sgqlc.types.Field(String, graphql_name="toBlockType")


class BlockTimesBulkQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("ids",)
    ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ID))), graphql_name="ids"
    )


class BlockUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "color", "site_ids", "block_times", "surgeon_ids")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    block_times = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(BlockTimeInput))),
        graphql_name="blockTimes",
    )
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="surgeonIds"
    )


class BlockUtilizationInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("min_date", "max_date", "site_id")
    min_date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minDate")
    max_date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxDate")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")


class BoardConfigCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "page_size",
        "page_duration",
        "blur_video",
        "org_id",
        "site_id",
        "room_ids",
        "board_view_type",
        "enable_video",
        "zoom_percent",
        "show_closed_rooms",
    )
    id = sgqlc.types.Field(UUID, graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    page_size = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageSize")
    page_duration = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageDuration")
    blur_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="blurVideo")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    board_view_type = sgqlc.types.Field(
        sgqlc.types.non_null(BoardViewType), graphql_name="boardViewType"
    )
    enable_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="enableVideo")
    zoom_percent = sgqlc.types.Field(Int, graphql_name="zoomPercent")
    show_closed_rooms = sgqlc.types.Field(Boolean, graphql_name="showClosedRooms")


class BoardConfigDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("board_config_id",)
    board_config_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="boardConfigId")


class BoardConfigQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_ids", "room_ids", "organization_ids")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="roomIds"
    )
    organization_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="organizationIds"
    )


class BoardConfigUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "board_config_id",
        "name",
        "page_size",
        "page_duration",
        "blur_video",
        "room_ids",
        "board_view_type",
        "enable_video",
        "zoom_percent",
        "show_closed_rooms",
    )
    board_config_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="boardConfigId")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    page_size = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageSize")
    page_duration = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageDuration")
    blur_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="blurVideo")
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    board_view_type = sgqlc.types.Field(
        sgqlc.types.non_null(BoardViewType), graphql_name="boardViewType"
    )
    enable_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="enableVideo")
    zoom_percent = sgqlc.types.Field(Int, graphql_name="zoomPercent")
    show_closed_rooms = sgqlc.types.Field(Boolean, graphql_name="showClosedRooms")


class CameraCaptureLatestImageInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("camera_id", "image_uri", "capture_time")
    camera_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cameraId")
    image_uri = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="imageUri")
    capture_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="captureTime")


class CameraCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "organization_id",
        "site_id",
        "room_id",
        "rtsp_url",
        "labels",
        "family",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    rtsp_url = sgqlc.types.Field(String, graphql_name="rtspUrl")
    labels = sgqlc.types.Field(JSON, graphql_name="labels")
    family = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="family")


class CameraUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "rtsp_url", "labels", "family")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    rtsp_url = sgqlc.types.Field(String, graphql_name="rtspUrl")
    labels = sgqlc.types.Field(JSON, graphql_name="labels")
    family = sgqlc.types.Field(String, graphql_name="family")


class CapacityConstraintInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("count", "start_time")
    count = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="count")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")


class CaseDurationPredictionQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("surgeon_id", "procedure", "additional_procedures")
    surgeon_id = sgqlc.types.Field(ID, graphql_name="surgeonId")
    procedure = sgqlc.types.Field(String, graphql_name="procedure")
    additional_procedures = sgqlc.types.Field(
        sgqlc.types.list_of(String), graphql_name="additionalProcedures"
    )


class CaseDurationProceduresQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("procedure_term", "surgeon_id")
    procedure_term = sgqlc.types.Field(String, graphql_name="procedureTerm")
    surgeon_id = sgqlc.types.Field(ID, graphql_name="surgeonId")


class CaseDurationSurgeonsQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("surgeon_term", "surgeon_id")
    surgeon_term = sgqlc.types.Field(String, graphql_name="surgeonTerm")
    surgeon_id = sgqlc.types.Field(ID, graphql_name="surgeonId")


class CaseEhrMessageQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "org_id",
        "min_event_time",
        "max_event_time",
        "case_id",
        "event_types",
        "raw_message_text_search",
        "no_case_id",
        "latest_per_case_id",
    )
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    min_event_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEventTime")
    max_event_time = sgqlc.types.Field(DateTime, graphql_name="maxEventTime")
    case_id = sgqlc.types.Field(String, graphql_name="caseId")
    event_types = sgqlc.types.Field(sgqlc.types.list_of(String), graphql_name="eventTypes")
    raw_message_text_search = sgqlc.types.Field(String, graphql_name="rawMessageTextSearch")
    no_case_id = sgqlc.types.Field(Boolean, graphql_name="noCaseId")
    latest_per_case_id = sgqlc.types.Field(Boolean, graphql_name="latestPerCaseId")


class CaseFlagUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "case_id", "flag_type", "archived", "org_id", "site_id")
    id = sgqlc.types.Field(ID, graphql_name="id")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    flag_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="flagType")
    archived = sgqlc.types.Field(Boolean, graphql_name="archived")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")


class CaseForecastForCaseInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_id",
        "room_id",
        "site_id",
        "organization_id",
        "forecast_start_time",
        "forecast_end_time",
        "forecast_variant",
        "forecast_status",
        "static_duration_minutes",
        "pythia_duration_minutes",
        "static_duration_end_time",
        "transformer_end_time",
        "pythia_end_time",
        "turnover_duration_minutes",
        "static_start_offset_minutes",
        "is_overtime",
        "is_auto_follow",
        "pythia_prediction_tag",
        "case_start_source",
        "bayesian_duration_minutes",
        "bayesian_end_time",
    )
    case_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="caseId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="organizationId")
    forecast_start_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastStartTime"
    )
    forecast_end_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastEndTime"
    )
    forecast_variant = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="forecastVariant"
    )
    forecast_status = sgqlc.types.Field(
        sgqlc.types.non_null(CaseForecastStatus), graphql_name="forecastStatus"
    )
    static_duration_minutes = sgqlc.types.Field(Float, graphql_name="staticDurationMinutes")
    pythia_duration_minutes = sgqlc.types.Field(Float, graphql_name="pythiaDurationMinutes")
    static_duration_end_time = sgqlc.types.Field(DateTime, graphql_name="staticDurationEndTime")
    transformer_end_time = sgqlc.types.Field(DateTime, graphql_name="transformerEndTime")
    pythia_end_time = sgqlc.types.Field(DateTime, graphql_name="pythiaEndTime")
    turnover_duration_minutes = sgqlc.types.Field(Float, graphql_name="turnoverDurationMinutes")
    static_start_offset_minutes = sgqlc.types.Field(Float, graphql_name="staticStartOffsetMinutes")
    is_overtime = sgqlc.types.Field(Boolean, graphql_name="isOvertime")
    is_auto_follow = sgqlc.types.Field(Boolean, graphql_name="isAutoFollow")
    pythia_prediction_tag = sgqlc.types.Field(String, graphql_name="pythiaPredictionTag")
    case_start_source = sgqlc.types.Field(String, graphql_name="caseStartSource")
    bayesian_duration_minutes = sgqlc.types.Field(Float, graphql_name="bayesianDurationMinutes")
    bayesian_end_time = sgqlc.types.Field(DateTime, graphql_name="bayesianEndTime")


class CaseForecastQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_ids",
        "min_start_time",
        "max_start_time",
        "forecast_variants",
        "room_ids",
        "site_ids",
        "forecast_statuses",
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    forecast_variants = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="forecastVariants"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="roomIds"
    )
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="siteIds"
    )
    forecast_statuses = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseForecastStatus)),
        graphql_name="forecastStatuses",
    )


class CaseForecastUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "case_id",
        "room_id",
        "site_id",
        "organization_id",
        "forecast_start_time",
        "forecast_end_time",
        "forecast_variant",
        "forecast_status",
        "pythia_duration_minutes",
        "static_duration_minutes",
        "bayesian_duration_minutes",
        "static_duration_end_time",
        "transformer_end_time",
        "pythia_end_time",
        "bayesian_end_time",
        "turnover_duration_minutes",
        "static_start_offset_minutes",
        "is_overtime",
        "is_auto_follow",
        "pythia_prediction_tag",
        "case_start_source",
    )
    id = sgqlc.types.Field(ID, graphql_name="id")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="caseId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="organizationId")
    forecast_start_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastStartTime"
    )
    forecast_end_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastEndTime"
    )
    forecast_variant = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="forecastVariant"
    )
    forecast_status = sgqlc.types.Field(
        sgqlc.types.non_null(CaseForecastStatus), graphql_name="forecastStatus"
    )
    pythia_duration_minutes = sgqlc.types.Field(Float, graphql_name="pythiaDurationMinutes")
    static_duration_minutes = sgqlc.types.Field(Float, graphql_name="staticDurationMinutes")
    bayesian_duration_minutes = sgqlc.types.Field(Float, graphql_name="bayesianDurationMinutes")
    static_duration_end_time = sgqlc.types.Field(DateTime, graphql_name="staticDurationEndTime")
    transformer_end_time = sgqlc.types.Field(DateTime, graphql_name="transformerEndTime")
    pythia_end_time = sgqlc.types.Field(DateTime, graphql_name="pythiaEndTime")
    bayesian_end_time = sgqlc.types.Field(DateTime, graphql_name="bayesianEndTime")
    turnover_duration_minutes = sgqlc.types.Field(Float, graphql_name="turnoverDurationMinutes")
    static_start_offset_minutes = sgqlc.types.Field(Float, graphql_name="staticStartOffsetMinutes")
    is_overtime = sgqlc.types.Field(Boolean, graphql_name="isOvertime")
    is_auto_follow = sgqlc.types.Field(Boolean, graphql_name="isAutoFollow")
    pythia_prediction_tag = sgqlc.types.Field(String, graphql_name="pythiaPredictionTag")
    case_start_source = sgqlc.types.Field(String, graphql_name="caseStartSource")


class CaseHistoryQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "min_end_time",
        "max_end_time",
        "min_updated_time",
        "max_updated_time",
        "min_created_time",
        "max_created_time",
        "site_ids",
        "room_ids",
        "staff_ids",
        "procedure_ids",
        "case_classification_types_ids",
        "case_ids",
        "status",
        "is_add_ons",
        "case_flags",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    min_end_time = sgqlc.types.Field(DateTime, graphql_name="minEndTime")
    max_end_time = sgqlc.types.Field(DateTime, graphql_name="maxEndTime")
    min_updated_time = sgqlc.types.Field(DateTime, graphql_name="minUpdatedTime")
    max_updated_time = sgqlc.types.Field(DateTime, graphql_name="maxUpdatedTime")
    min_created_time = sgqlc.types.Field(DateTime, graphql_name="minCreatedTime")
    max_created_time = sgqlc.types.Field(DateTime, graphql_name="maxCreatedTime")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    procedure_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="procedureIds"
    )
    case_classification_types_ids = sgqlc.types.Field(
        sgqlc.types.list_of(ID), graphql_name="caseClassificationTypesIds"
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    status = sgqlc.types.Field(String, graphql_name="status")
    is_add_ons = sgqlc.types.Field(sgqlc.types.list_of(Boolean), graphql_name="isAddOns")
    case_flags = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="caseFlags"
    )


class CaseLabelUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("case_id", "option_id", "id", "archived_time")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    option_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="optionId")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")


class CaseNotePlanUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "case_id", "note", "org_id", "site_id")
    id = sgqlc.types.Field(UUID, graphql_name="id")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="caseId")
    note = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="note")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")


class CaseProceduresUpsertAndArchiveInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("case_id", "procedure_id", "hierarchy", "anesthesia_id")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    procedure_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="procedureId")
    hierarchy = sgqlc.types.Field(Int, graphql_name="hierarchy")
    anesthesia_id = sgqlc.types.Field(UUID, graphql_name="anesthesiaId")


class CaseProceduresUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("case_id", "procedure_id", "hierarchy", "anesthesia_id")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    procedure_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="procedureId")
    hierarchy = sgqlc.types.Field(Int, graphql_name="hierarchy")
    anesthesia_id = sgqlc.types.Field(UUID, graphql_name="anesthesiaId")


class CaseStaffPlanInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("staff_ids", "include_archived")
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    include_archived = sgqlc.types.Field(Boolean, graphql_name="includeArchived")


class CaseStaffPlanUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "staff_id", "case_id", "org_id", "site_id", "role", "archived_time")
    id = sgqlc.types.Field(UUID, graphql_name="id")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="staffId")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="caseId")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    role = sgqlc.types.Field(String, graphql_name="role")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")


class CaseStaffUpsertAndArchiveInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("case_id", "staff_id", "role")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="staffId")
    role = sgqlc.types.Field(String, graphql_name="role")


class CaseStaffUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("case_id", "staff_id", "role")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="staffId")
    role = sgqlc.types.Field(String, graphql_name="role")


class CaseToBlockInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("min_date", "max_date", "site_id")
    min_date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minDate")
    max_date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxDate")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")


class CaseToBlockOverrideInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("min_date", "max_date", "block_ids", "case_ids")
    min_date = sgqlc.types.Field(DateTime, graphql_name="minDate")
    max_date = sgqlc.types.Field(DateTime, graphql_name="maxDate")
    block_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="blockIds"
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="caseIds"
    )


class CaseToBlockOverrideUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_id",
        "block_id",
        "block_date",
        "user_id",
        "utilized_procedure_minutes",
        "utilized_turnover_minutes",
        "note",
    )
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    block_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="blockId")
    block_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="blockDate")
    user_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="userId")
    utilized_procedure_minutes = sgqlc.types.Field(Int, graphql_name="utilizedProcedureMinutes")
    utilized_turnover_minutes = sgqlc.types.Field(Int, graphql_name="utilizedTurnoverMinutes")
    note = sgqlc.types.Field(String, graphql_name="note")


class ClusterCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "enable_audio", "sites")
    id = sgqlc.types.Field(ID, graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    enable_audio = sgqlc.types.Field(Boolean, graphql_name="enableAudio")
    sites = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="sites",
    )


class ClusterUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "enable_audio", "sites")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    enable_audio = sgqlc.types.Field(Boolean, graphql_name="enableAudio")
    sites = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="sites",
    )


class ContactInformationSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_ids",
        "staff_ids",
        "ids",
        "contact_information_values",
        "types",
        "event_type_ids",
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    ids = sgqlc.types.Field(sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="ids")
    contact_information_values = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="contactInformationValues"
    )
    types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="types"
    )
    event_type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventTypeIds"
    )


class CustomPhaseConfigDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class CustomPhaseConfigUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "start_event_type", "end_event_type", "name", "description")
    id = sgqlc.types.Field(ID, graphql_name="id")
    start_event_type = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="startEventType"
    )
    end_event_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="endEventType")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")


class DayOfWeekInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_time", "end_time", "constraints")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="endTime")
    constraints = sgqlc.types.Field(
        sgqlc.types.list_of(CapacityConstraintInput), graphql_name="constraints"
    )


class DefaultSiteClosuresCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_date", "end_date")
    start_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="startDate")
    end_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="endDate")


class EmailAvailableTimesHtmlInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_date", "end_date", "slots")
    start_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="startDate")
    end_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="endDate")
    slots = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(AvailableTimeSlotInput))),
        graphql_name="slots",
    )


class EmailAvailableTimesInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "start_date",
        "end_date",
        "sender_name",
        "sender_email",
        "recipients",
        "slots",
    )
    start_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="startDate")
    end_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="endDate")
    sender_name = sgqlc.types.Field(String, graphql_name="senderName")
    sender_email = sgqlc.types.Field(String, graphql_name="senderEmail")
    recipients = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="recipients",
    )
    slots = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(AvailableTimeSlotInput))),
        graphql_name="slots",
    )


class EventCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "start_time",
        "process_time",
        "organization_id",
        "site_id",
        "room_id",
        "camera_id",
        "source",
        "source_type",
        "model_version",
        "confidence",
        "labels",
        "notes",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    process_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="processTime")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    camera_id = sgqlc.types.Field(String, graphql_name="cameraId")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    confidence = sgqlc.types.Field(Float, graphql_name="confidence")
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")


class EventDashboardVisibilityDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("event_type_id",)
    event_type_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="eventTypeId")


class EventDashboardVisibilityUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("event_type_id", "org_id_filter")
    event_type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="eventTypeId")
    org_id_filter = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="orgIdFilter"
    )


class EventDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "source", "source_type", "publish_changelog")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    publish_changelog = sgqlc.types.Field(Boolean, graphql_name="publishChangelog")


class EventHistorySearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "camera_id",
        "source_type",
        "source_types",
        "model_version",
        "min_confidence",
        "event_type",
        "event_names",
        "exclude_event_names",
        "include_dashboard_events_only",
        "labels",
        "notes",
        "include_deleted",
        "organization_id",
        "site_id",
        "site_ids",
        "room_id",
        "room_ids",
        "event_ids",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    camera_id = sgqlc.types.Field(ID, graphql_name="cameraId")
    source_type = sgqlc.types.Field(String, graphql_name="sourceType")
    source_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="sourceTypes"
    )
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    min_confidence = sgqlc.types.Field(Float, graphql_name="minConfidence")
    event_type = sgqlc.types.Field(String, graphql_name="eventType")
    event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventNames"
    )
    exclude_event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="excludeEventNames"
    )
    include_dashboard_events_only = sgqlc.types.Field(
        Boolean, graphql_name="includeDashboardEventsOnly"
    )
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    include_deleted = sgqlc.types.Field(Boolean, graphql_name="includeDeleted")
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_id = sgqlc.types.Field(ID, graphql_name="siteId")
    site_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="siteIds")
    room_id = sgqlc.types.Field(ID, graphql_name="roomId")
    room_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="roomIds")
    event_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="eventIds")


class EventLabelOptionCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name")
    id = sgqlc.types.Field(ID, graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")


class EventLabelOptionDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class EventLabelOptionUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")


class EventNotficationQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("staff_ids", "event_type_ids", "case_ids", "room_ids", "event_ids")
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="staffIds"
    )
    event_type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventTypeIds"
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="caseIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="roomIds"
    )
    event_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventIds"
    )


class EventSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "camera_id",
        "source_type",
        "source_types",
        "model_version",
        "min_confidence",
        "event_type",
        "event_names",
        "exclude_event_names",
        "include_dashboard_events_only",
        "labels",
        "notes",
        "include_deleted",
        "organization_id",
        "site_id",
        "site_ids",
        "room_id",
        "room_ids",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    camera_id = sgqlc.types.Field(ID, graphql_name="cameraId")
    source_type = sgqlc.types.Field(String, graphql_name="sourceType")
    source_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="sourceTypes"
    )
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    min_confidence = sgqlc.types.Field(Float, graphql_name="minConfidence")
    event_type = sgqlc.types.Field(String, graphql_name="eventType")
    event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventNames"
    )
    exclude_event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="excludeEventNames"
    )
    include_dashboard_events_only = sgqlc.types.Field(
        Boolean, graphql_name="includeDashboardEventsOnly"
    )
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    include_deleted = sgqlc.types.Field(Boolean, graphql_name="includeDeleted")
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_id = sgqlc.types.Field(ID, graphql_name="siteId")
    site_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="siteIds")
    room_id = sgqlc.types.Field(ID, graphql_name="roomId")
    room_ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="roomIds")


class EventTypeCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "type", "name", "description", "color", "hidden")
    id = sgqlc.types.Field(ID, graphql_name="id")
    type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="type")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    hidden = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="hidden")


class EventTypeUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "type", "name", "description", "color", "hidden")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    type = sgqlc.types.Field(String, graphql_name="type")
    name = sgqlc.types.Field(String, graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    color = sgqlc.types.Field(String, graphql_name="color")
    hidden = sgqlc.types.Field(Boolean, graphql_name="hidden")


class EventUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "start_time",
        "camera_id",
        "labels",
        "notes",
        "source",
        "source_type",
        "publish_changelog",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    start_time = sgqlc.types.Field(DateTime, graphql_name="startTime")
    camera_id = sgqlc.types.Field(ID, graphql_name="cameraId")
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    publish_changelog = sgqlc.types.Field(Boolean, graphql_name="publishChangelog")


class EventUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "event_type",
        "event_name",
        "start_time",
        "process_timestamp",
        "organization_id",
        "site_id",
        "room_id",
        "camera_id",
        "event_type_id",
        "source",
        "source_type",
        "model_version",
        "confidence",
        "labels",
        "notes",
        "etag",
        "publish_changelog",
        "event_matching_status",
    )
    id = sgqlc.types.Field(ID, graphql_name="id")
    event_type = sgqlc.types.Field(String, graphql_name="eventType")
    event_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="eventName")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    process_timestamp = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="processTimestamp"
    )
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    camera_id = sgqlc.types.Field(String, graphql_name="cameraId")
    event_type_id = sgqlc.types.Field(String, graphql_name="eventTypeId")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    confidence = sgqlc.types.Field(Float, graphql_name="confidence")
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    etag = sgqlc.types.Field(String, graphql_name="etag")
    publish_changelog = sgqlc.types.Field(Boolean, graphql_name="publishChangelog")
    event_matching_status = sgqlc.types.Field(
        EventMatchingStatus, graphql_name="eventMatchingStatus"
    )


class GQLTurnoverLabelQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("type",)
    type = sgqlc.types.Field(String, graphql_name="type")


class HighlightArchiveInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class HighlightCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "description",
        "organization_id",
        "site_id",
        "room_id",
        "camera_id",
        "assigned_user_ids",
        "start_time",
        "end_time",
        "category",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    camera_id = sgqlc.types.Field(String, graphql_name="cameraId")
    assigned_user_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="assignedUserIds",
    )
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    category = sgqlc.types.Field(String, graphql_name="category")


class HighlightDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class HighlightFeedbackCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "comment", "rating", "highlight_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    comment = sgqlc.types.Field(String, graphql_name="comment")
    rating = sgqlc.types.Field(Int, graphql_name="rating")
    highlight_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="highlightId")


class HighlightFeedbackSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("min_time", "max_time")
    min_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minTime")
    max_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxTime")


class HighlightFeedbackUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "comment", "rating", "highlight_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    comment = sgqlc.types.Field(String, graphql_name="comment")
    rating = sgqlc.types.Field(Int, graphql_name="rating")
    highlight_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="highlightId")


class HighlightInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("status", "min_time", "max_time")
    status = sgqlc.types.Field(String, graphql_name="status")
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")


class HighlightSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "organization_ids",
        "site_ids",
        "room_ids",
        "categories",
        "assigned_user_ids",
        "feedback_status",
    )
    min_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minTime")
    max_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxTime")
    organization_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="organizationIds"
    )
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    categories = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="categories"
    )
    assigned_user_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="assignedUserIds"
    )
    feedback_status = sgqlc.types.Field(FeedbackStatus, graphql_name="feedbackStatus")


class HighlightUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "description",
        "organization_id",
        "site_id",
        "room_id",
        "camera_id",
        "assigned_user_ids",
        "start_time",
        "end_time",
        "category",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    camera_id = sgqlc.types.Field(String, graphql_name="cameraId")
    assigned_user_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="assignedUserIds",
    )
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    category = sgqlc.types.Field(String, graphql_name="category")


class LiveCameraImagesInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("camera_ids",)
    camera_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="cameraIds"
    )


class MatchCaseInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_id",
        "phase_id",
        "phase_etag",
        "case_match_type",
        "explanation_for_change",
    )
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    phase_id = sgqlc.types.Field(UUID, graphql_name="phaseId")
    phase_etag = sgqlc.types.Field(UUID, graphql_name="phaseEtag")
    case_match_type = sgqlc.types.Field(CaseMatchingStatus, graphql_name="caseMatchType")
    explanation_for_change = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="explanationForChange"
    )


class MeasurementPeriodDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class MeasurementPeriodQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "site_id",
        "annotation_task_type_id",
        "measurement_period_start",
        "measurement_period_end",
        "names",
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    annotation_task_type_id = sgqlc.types.Field(String, graphql_name="annotationTaskTypeId")
    measurement_period_start = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodStart"
    )
    measurement_period_end = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodEnd"
    )
    names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="names"
    )


class MeasurementPeriodUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "measurement_period_start",
        "measurement_period_end",
        "annotation_task_type_id",
        "site_id",
        "room_ids",
        "days_of_week",
        "iso_days_of_week",
    )
    id = sgqlc.types.Field(ID, graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    measurement_period_start = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodStart"
    )
    measurement_period_end = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodEnd"
    )
    annotation_task_type_id = sgqlc.types.Field(
        sgqlc.types.non_null(ID), graphql_name="annotationTaskTypeId"
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    days_of_week = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(DayOfWeek)), graphql_name="daysOfWeek"
    )
    iso_days_of_week = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(Int)), graphql_name="isoDaysOfWeek"
    )


class ObservationCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "observation_time",
        "recorded_time",
        "organization_id",
        "case_id",
        "type_id",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    observation_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="observationTime"
    )
    recorded_time = sgqlc.types.Field(DateTime, graphql_name="recordedTime")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")


class ObservationDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class ObservationSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "organization_id",
        "site_ids",
        "room_ids",
        "case_ids",
        "type_ids",
        "min_observation_time",
        "max_observation_time",
        "min_recorded_time",
        "max_recorded_time",
    )
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="typeIds"
    )
    min_observation_time = sgqlc.types.Field(DateTime, graphql_name="minObservationTime")
    max_observation_time = sgqlc.types.Field(DateTime, graphql_name="maxObservationTime")
    min_recorded_time = sgqlc.types.Field(DateTime, graphql_name="minRecordedTime")
    max_recorded_time = sgqlc.types.Field(DateTime, graphql_name="maxRecordedTime")


class ObservationTypeNamesInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id", "type_ids")
    org_id = sgqlc.types.Field(ID, graphql_name="orgId")
    type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="typeIds"
    )


class ObservationTypeNamesInputForCustomPhases(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id",)
    org_id = sgqlc.types.Field(ID, graphql_name="orgId")


class ObservationUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("organization_id", "case_id", "type_id", "observation_time", "recorded_time")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")
    observation_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="observationTime"
    )
    recorded_time = sgqlc.types.Field(DateTime, graphql_name="recordedTime")


class OccupancyBucketInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id", "min_time", "max_time")
    room_id = sgqlc.types.Field(String, graphql_name="roomId")
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")


class OrderBy(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("sort", "direction")
    sort = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sort")
    direction = sgqlc.types.Field(Direction, graphql_name="direction")


class OrganizationCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "auth0_org_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    auth0_org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="auth0OrgId")


class OrganizationUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "auth0_org_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    auth0_org_id = sgqlc.types.Field(String, graphql_name="auth0OrgId")


class PhaseCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "org_id",
        "site_id",
        "room_id",
        "type_id",
        "start_event_id",
        "end_event_id",
        "case_id",
        "source_type",
        "status",
        "invalidation_reason",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")
    start_event_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="startEventId")
    end_event_id = sgqlc.types.Field(ID, graphql_name="endEventId")
    case_id = sgqlc.types.Field(ID, graphql_name="caseId")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    status = sgqlc.types.Field(PhaseStatus, graphql_name="status")
    invalidation_reason = sgqlc.types.Field(String, graphql_name="invalidationReason")


class PhaseDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")


class PhaseQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "min_end_time",
        "max_end_time",
        "min_updated_time",
        "max_updated_time",
        "min_created_time",
        "max_created_time",
        "organization_id",
        "site_ids",
        "room_ids",
        "show_human_ground_truth_data",
        "source_type",
        "type",
        "case_ids",
        "max_duration",
        "statuses",
        "ensure_phase_start_time_has_not_elapsed",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    min_end_time = sgqlc.types.Field(DateTime, graphql_name="minEndTime")
    max_end_time = sgqlc.types.Field(DateTime, graphql_name="maxEndTime")
    min_updated_time = sgqlc.types.Field(DateTime, graphql_name="minUpdatedTime")
    max_updated_time = sgqlc.types.Field(DateTime, graphql_name="maxUpdatedTime")
    min_created_time = sgqlc.types.Field(DateTime, graphql_name="minCreatedTime")
    max_created_time = sgqlc.types.Field(DateTime, graphql_name="maxCreatedTime")
    organization_id = sgqlc.types.Field(ID, graphql_name="organizationId")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    show_human_ground_truth_data = sgqlc.types.Field(
        Boolean, graphql_name="showHumanGroundTruthData"
    )
    source_type = sgqlc.types.Field(String, graphql_name="sourceType")
    type = sgqlc.types.Field(PhaseType, graphql_name="type")
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    max_duration = sgqlc.types.Field(Duration, graphql_name="maxDuration")
    statuses = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(PhaseStatus)), graphql_name="statuses"
    )
    ensure_phase_start_time_has_not_elapsed = sgqlc.types.Field(
        Boolean, graphql_name="ensurePhaseStartTimeHasNotElapsed"
    )


class PhaseRelationshipCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("parent_phase_id", "child_phase_id", "org_id")
    parent_phase_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="parentPhaseId")
    child_phase_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="childPhaseId")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class PhaseRelationshipDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("parent_phase_id", "child_phase_id")
    parent_phase_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="parentPhaseId")
    child_phase_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="childPhaseId")


class PhaseUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "case_id",
        "type_id",
        "start_event_id",
        "end_event_id",
        "source_type",
        "status",
        "invalidation_reason",
        "etag",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    case_id = sgqlc.types.Field(ID, graphql_name="caseId")
    type_id = sgqlc.types.Field(String, graphql_name="typeId")
    start_event_id = sgqlc.types.Field(ID, graphql_name="startEventId")
    end_event_id = sgqlc.types.Field(ID, graphql_name="endEventId")
    source_type = sgqlc.types.Field(String, graphql_name="sourceType")
    status = sgqlc.types.Field(PhaseStatus, graphql_name="status")
    invalidation_reason = sgqlc.types.Field(String, graphql_name="invalidationReason")
    etag = sgqlc.types.Field(String, graphql_name="etag")


class PhaseUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "org_id",
        "site_id",
        "room_id",
        "type_id",
        "start_event_id",
        "end_event_id",
        "case_id",
        "source_type",
        "status",
        "invalidation_reason",
        "etag",
        "event_matching_status",
    )
    id = sgqlc.types.Field(ID, graphql_name="id")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")
    start_event_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="startEventId")
    end_event_id = sgqlc.types.Field(ID, graphql_name="endEventId")
    case_id = sgqlc.types.Field(ID, graphql_name="caseId")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    status = sgqlc.types.Field(sgqlc.types.non_null(PhaseStatus), graphql_name="status")
    invalidation_reason = sgqlc.types.Field(String, graphql_name="invalidationReason")
    etag = sgqlc.types.Field(String, graphql_name="etag")
    event_matching_status = sgqlc.types.Field(
        EventMatchingStatus, graphql_name="eventMatchingStatus"
    )


class ProcedureQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id", "case_id", "names", "hierarchy")
    org_id = sgqlc.types.Field(String, graphql_name="orgId")
    case_id = sgqlc.types.Field(String, graphql_name="caseId")
    names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="names"
    )
    hierarchy = sgqlc.types.Field(Int, graphql_name="hierarchy")


class ProcedureUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("name", "org_id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class ProcessCaseDerivedPropertiesInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "date")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    date = sgqlc.types.Field(Date, graphql_name="date")


class RoomClosureCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id", "start_time", "end_time")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")


class RoomClosureDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_closure_id",)
    room_closure_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomClosureId")


class RoomClosureQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id", "room_ids", "site_id", "min_end_time", "max_start_time")
    room_id = sgqlc.types.Field(String, graphql_name="roomId")
    room_ids = sgqlc.types.Field(sgqlc.types.list_of(String), graphql_name="roomIds")
    site_id = sgqlc.types.Field(String, graphql_name="siteId")
    min_end_time = sgqlc.types.Field(DateTime, graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")


class RoomCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "default_camera_id",
        "site_id",
        "organization_id",
        "labels",
        "is_forecasting_enabled",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    default_camera_id = sgqlc.types.Field(String, graphql_name="defaultCameraId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    labels = sgqlc.types.Field(JSON, graphql_name="labels")
    is_forecasting_enabled = sgqlc.types.Field(Boolean, graphql_name="isForecastingEnabled")


class RoomEventSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "camera_id",
        "source_type",
        "source_types",
        "model_version",
        "min_confidence",
        "event_type",
        "event_names",
        "exclude_event_names",
        "include_dashboard_events_only",
        "labels",
        "notes",
        "include_deleted",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    camera_id = sgqlc.types.Field(ID, graphql_name="cameraId")
    source_type = sgqlc.types.Field(String, graphql_name="sourceType")
    source_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="sourceTypes"
    )
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    min_confidence = sgqlc.types.Field(Float, graphql_name="minConfidence")
    event_type = sgqlc.types.Field(String, graphql_name="eventType")
    event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventNames"
    )
    exclude_event_names = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="excludeEventNames"
    )
    include_dashboard_events_only = sgqlc.types.Field(
        Boolean, graphql_name="includeDashboardEventsOnly"
    )
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    include_deleted = sgqlc.types.Field(Boolean, graphql_name="includeDeleted")


class RoomFirstCaseConfigDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id",)
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")


class RoomFirstCaseConfigUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "room_id",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    sunday = sgqlc.types.Field(DayOfWeekInput, graphql_name="sunday")
    monday = sgqlc.types.Field(DayOfWeekInput, graphql_name="monday")
    tuesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="tuesday")
    wednesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="wednesday")
    thursday = sgqlc.types.Field(DayOfWeekInput, graphql_name="thursday")
    friday = sgqlc.types.Field(DayOfWeekInput, graphql_name="friday")
    saturday = sgqlc.types.Field(DayOfWeekInput, graphql_name="saturday")


class RoomPrimeTimeConfigDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id",)
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")


class RoomPrimeTimeConfigUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "room_id",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    sunday = sgqlc.types.Field("TimeRangeInput", graphql_name="sunday")
    monday = sgqlc.types.Field("TimeRangeInput", graphql_name="monday")
    tuesday = sgqlc.types.Field("TimeRangeInput", graphql_name="tuesday")
    wednesday = sgqlc.types.Field("TimeRangeInput", graphql_name="wednesday")
    thursday = sgqlc.types.Field("TimeRangeInput", graphql_name="thursday")
    friday = sgqlc.types.Field("TimeRangeInput", graphql_name="friday")
    saturday = sgqlc.types.Field("TimeRangeInput", graphql_name="saturday")


class RoomSetTagsInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("tag_ids", "room_id")
    tag_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ID))), graphql_name="tagIds"
    )
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")


class RoomTagCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("name", "color", "org_id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(String, graphql_name="color")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")


class RoomTagRenameInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("tag_id", "name")
    tag_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="tagId")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")


class RoomTagUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("tag_id", "name", "color")
    tag_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="tagId")
    name = sgqlc.types.Field(String, graphql_name="name")
    color = sgqlc.types.Field(String, graphql_name="color")


class RoomUpdateConfigurationInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "privacy_enabled")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    privacy_enabled = sgqlc.types.Field(Boolean, graphql_name="privacyEnabled")


class RoomUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "default_camera_id",
        "privacy_enabled",
        "is_forecasting_enabled",
        "sort_key",
        "labels",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    default_camera_id = sgqlc.types.Field(String, graphql_name="defaultCameraId")
    privacy_enabled = sgqlc.types.Field(Boolean, graphql_name="privacyEnabled")
    is_forecasting_enabled = sgqlc.types.Field(Boolean, graphql_name="isForecastingEnabled")
    sort_key = sgqlc.types.Field(String, graphql_name="sortKey")
    labels = sgqlc.types.Field(JSON, graphql_name="labels")


class ScheduledCaseQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_time",
        "max_time",
        "min_start_time",
        "max_start_time",
        "min_end_time",
        "max_end_time",
        "min_updated_time",
        "max_updated_time",
        "min_created_time",
        "max_created_time",
        "site_ids",
        "room_ids",
        "staff_ids",
        "procedure_ids",
        "case_classification_types_ids",
        "case_ids",
        "status",
        "is_add_ons",
        "case_flags",
    )
    min_time = sgqlc.types.Field(DateTime, graphql_name="minTime")
    max_time = sgqlc.types.Field(DateTime, graphql_name="maxTime")
    min_start_time = sgqlc.types.Field(DateTime, graphql_name="minStartTime")
    max_start_time = sgqlc.types.Field(DateTime, graphql_name="maxStartTime")
    min_end_time = sgqlc.types.Field(DateTime, graphql_name="minEndTime")
    max_end_time = sgqlc.types.Field(DateTime, graphql_name="maxEndTime")
    min_updated_time = sgqlc.types.Field(DateTime, graphql_name="minUpdatedTime")
    max_updated_time = sgqlc.types.Field(DateTime, graphql_name="maxUpdatedTime")
    min_created_time = sgqlc.types.Field(DateTime, graphql_name="minCreatedTime")
    max_created_time = sgqlc.types.Field(DateTime, graphql_name="maxCreatedTime")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="roomIds"
    )
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    procedure_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="procedureIds"
    )
    case_classification_types_ids = sgqlc.types.Field(
        sgqlc.types.list_of(ID), graphql_name="caseClassificationTypesIds"
    )
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    status = sgqlc.types.Field(String, graphql_name="status")
    is_add_ons = sgqlc.types.Field(sgqlc.types.list_of(Boolean), graphql_name="isAddOns")
    case_flags = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="caseFlags"
    )


class ServiceLineQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("ids", "org_id")
    ids = sgqlc.types.Field(sgqlc.types.list_of(String), graphql_name="ids")
    org_id = sgqlc.types.Field(String, graphql_name="orgId")


class SiteClosureCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "closure_date", "reason")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    closure_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="closureDate")
    reason = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="reason")


class SiteClosureDeleteInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_closure_id",)
    site_closure_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteClosureId")


class SiteClosureQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "closure_date")
    site_id = sgqlc.types.Field(String, graphql_name="siteId")
    closure_date = sgqlc.types.Field(Date, graphql_name="closureDate")


class SiteCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "organization_id", "timezone")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    timezone = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="timezone")


class SiteFirstCaseConfigUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "site_id",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    sunday = sgqlc.types.Field(DayOfWeekInput, graphql_name="sunday")
    monday = sgqlc.types.Field(DayOfWeekInput, graphql_name="monday")
    tuesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="tuesday")
    wednesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="wednesday")
    thursday = sgqlc.types.Field(DayOfWeekInput, graphql_name="thursday")
    friday = sgqlc.types.Field(DayOfWeekInput, graphql_name="friday")
    saturday = sgqlc.types.Field(DayOfWeekInput, graphql_name="saturday")


class SiteLaunchUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "actual_launch_date", "anticipated_launch_date")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    actual_launch_date = sgqlc.types.Field(Date, graphql_name="actualLaunchDate")
    anticipated_launch_date = sgqlc.types.Field(Date, graphql_name="anticipatedLaunchDate")


class SitePrimeTimeConfigUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "site_id",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    sunday = sgqlc.types.Field(DayOfWeekInput, graphql_name="sunday")
    monday = sgqlc.types.Field(DayOfWeekInput, graphql_name="monday")
    tuesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="tuesday")
    wednesday = sgqlc.types.Field(DayOfWeekInput, graphql_name="wednesday")
    thursday = sgqlc.types.Field(DayOfWeekInput, graphql_name="thursday")
    friday = sgqlc.types.Field(DayOfWeekInput, graphql_name="friday")
    saturday = sgqlc.types.Field(DayOfWeekInput, graphql_name="saturday")


class SiteUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "organization_id", "timezone")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    organization_id = sgqlc.types.Field(String, graphql_name="organizationId")
    timezone = sgqlc.types.Field(String, graphql_name="timezone")


class StaffCodeQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("code", "coding_system")
    code = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="code")
    coding_system = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="codingSystem")


class StaffCodeUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("staff_id", "coding_system", "code", "org_id")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="staffId")
    coding_system = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="codingSystem")
    code = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="code")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")


class StaffEventNotificationContactInformationSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("staff_ids", "event_type_ids", "org_ids")
    staff_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="staffIds"
    )
    event_type_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventTypeIds"
    )
    org_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="orgIds"
    )


class StaffEventsNotificationsInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "site_id",
        "confidence_threshold",
        "time_to_check",
        "site_name",
        "time_threshold",
        "time_window_to_search",
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    confidence_threshold = sgqlc.types.Field(Float, graphql_name="confidenceThreshold")
    time_to_check = sgqlc.types.Field(DateTime, graphql_name="timeToCheck")
    site_name = sgqlc.types.Field(String, graphql_name="siteName")
    time_threshold = sgqlc.types.Field(Duration, graphql_name="timeThreshold")
    time_window_to_search = sgqlc.types.Field(Duration, graphql_name="timeWindowToSearch")


class StaffQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "ids",
        "name",
        "org_id",
        "case_id",
        "staff_code",
        "only_primary_surgeons",
        "staff_roles",
    )
    ids = sgqlc.types.Field(sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="ids")
    name = sgqlc.types.Field(String, graphql_name="name")
    org_id = sgqlc.types.Field(String, graphql_name="orgId")
    case_id = sgqlc.types.Field(String, graphql_name="caseId")
    staff_code = sgqlc.types.Field(StaffCodeQueryInput, graphql_name="staffCode")
    only_primary_surgeons = sgqlc.types.Field(Boolean, graphql_name="onlyPrimarySurgeons")
    staff_roles = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseStaffRole)), graphql_name="staffRoles"
    )


class StaffUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("org_id", "external_staff_id", "first_name", "last_name")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    external_staff_id = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="externalStaffId"
    )
    first_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="firstName")
    last_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="lastName")


class StaffingNeedsRatioCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "organization_id",
        "site_id",
        "set_by_user_id",
        "ratio",
        "staff_role_id",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    set_by_user_id = sgqlc.types.Field(String, graphql_name="setByUserId")
    ratio = sgqlc.types.Field(Float, graphql_name="ratio")
    staff_role_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="staffRoleId")


class SubscriberEventNotificationUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("staff_id", "event_type_id", "archive")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(UUID), graphql_name="staffId")
    event_type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="eventTypeId")
    archive = sgqlc.types.Field(Boolean, graphql_name="archive")


class SubscriberUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "contact_information_id",
        "contact_information_value",
        "type",
        "first_name",
        "last_name",
        "subscriptions",
        "is_apella_employee",
    )
    contact_information_id = sgqlc.types.Field(UUID, graphql_name="contactInformationId")
    contact_information_value = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="contactInformationValue"
    )
    type = sgqlc.types.Field(sgqlc.types.non_null(ContactInformationType), graphql_name="type")
    first_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="firstName")
    last_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="lastName")
    subscriptions = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(sgqlc.types.non_null(SubscriberEventNotificationUpsertInput))
        ),
        graphql_name="subscriptions",
    )
    is_apella_employee = sgqlc.types.Field(Boolean, graphql_name="isApellaEmployee")


class TerminalCleanScoreUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("room_id", "date", "comments", "score")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="date")
    comments = sgqlc.types.Field(String, graphql_name="comments")
    score = sgqlc.types.Field(CleanScoreEnum, graphql_name="score")


class TimeRangeInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("start_time", "end_time")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="endTime")


class TurnoverGoalsUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("site_id", "org_id", "goal_minutes", "max_minutes")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    goal_minutes = sgqlc.types.Field(Int, graphql_name="goalMinutes")
    max_minutes = sgqlc.types.Field(Int, graphql_name="maxMinutes")


class TurnoverLabelNoteUpsertInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "note", "label_ids")
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    note = sgqlc.types.Field(String, graphql_name="note")
    label_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labelIds"
    )


class TurnoverQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = (
        "min_end_time",
        "max_start_time",
        "case_ids",
        "phase_ids",
        "turnover_id",
        "meets_inclusion_criteria",
    )
    min_end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="minEndTime")
    max_start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="maxStartTime")
    case_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="caseIds"
    )
    phase_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="phaseIds"
    )
    turnover_id = sgqlc.types.Field(String, graphql_name="turnoverId")
    meets_inclusion_criteria = sgqlc.types.Field(Boolean, graphql_name="meetsInclusionCriteria")


class UserFilterViewCreateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "url")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    url = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="url")


class UserFilterViewQueryInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("ids", "name")
    ids = sgqlc.types.Field(sgqlc.types.list_of(ID), graphql_name="ids")
    name = sgqlc.types.Field(String, graphql_name="name")


class UserFilterViewUpdateInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "url")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    url = sgqlc.types.Field(String, graphql_name="url")


class UsersSearchInput(sgqlc.types.Input):
    __schema__ = api_server_schema
    __field_names__ = ("role",)
    role = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="role")


########################################################################
# Output Objects and Interfaces
########################################################################
class AdministrativeSex(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("type", "text")
    type = sgqlc.types.Field(sgqlc.types.non_null(AdministrativeSexType), graphql_name="type")
    text = sgqlc.types.Field(String, graphql_name="text")


class AgeRange(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("min", "max")
    min = sgqlc.types.Field(Int, graphql_name="min")
    max = sgqlc.types.Field(Int, graphql_name="max")


class Anesthesia(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "org_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")


class AnesthesiaConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("AnesthesiaEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class AnesthesiaEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Anesthesia), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class AnesthesiasUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "anesthesias")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    anesthesias = sgqlc.types.Field(
        sgqlc.types.non_null(AnesthesiaConnection),
        graphql_name="anesthesias",
        args=sgqlc.types.ArgDict(
            (
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class AnnnotationTaskTypeCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_annotation_task_type")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_annotation_task_type = sgqlc.types.Field(
        sgqlc.types.non_null("AnnotationTaskType"), graphql_name="createdAnnotationTaskType"
    )


class AnnnotationTaskTypeUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_annotation_task_type")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    updated_annotation_task_type = sgqlc.types.Field(
        sgqlc.types.non_null("AnnotationTaskType"), graphql_name="updatedAnnotationTaskType"
    )


class AnnotationTask(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "start_time",
        "end_time",
        "status",
        "cancelled_reason",
        "updated_time",
        "org_id",
        "organization",
        "site_id",
        "site",
        "room_id",
        "room",
        "annotator_user_id",
        "annotator",
        "reviewer_user_id",
        "reviewer",
        "updated_by_user_id",
        "updated_by_user",
        "type_id",
        "type",
        "events",
        "context_events",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    status = sgqlc.types.Field(sgqlc.types.non_null(TaskStatus), graphql_name="status")
    cancelled_reason = sgqlc.types.Field(CancelledReason, graphql_name="cancelledReason")
    updated_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="updatedTime")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    annotator_user_id = sgqlc.types.Field(String, graphql_name="annotatorUserId")
    annotator = sgqlc.types.Field("User", graphql_name="annotator")
    reviewer_user_id = sgqlc.types.Field(String, graphql_name="reviewerUserId")
    reviewer = sgqlc.types.Field("User", graphql_name="reviewer")
    updated_by_user_id = sgqlc.types.Field(String, graphql_name="updatedByUserId")
    updated_by_user = sgqlc.types.Field("User", graphql_name="updatedByUser")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")
    type = sgqlc.types.Field(sgqlc.types.non_null("AnnotationTaskType"), graphql_name="type")
    events = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("Event"))),
        graphql_name="events",
    )
    context_events = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("Event"))),
        graphql_name="contextEvents",
    )


class AnnotationTaskBulkGenerate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class AnnotationTaskBulkUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "count")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    count = sgqlc.types.Field(Int, graphql_name="count")


class AnnotationTaskConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records", "counts")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("AnnotationTaskEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")
    counts = sgqlc.types.Field("AnnotationTasksCount", graphql_name="counts")


class AnnotationTaskEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(AnnotationTask), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class AnnotationTaskExit(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "task")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    task = sgqlc.types.Field(AnnotationTask, graphql_name="task")


class AnnotationTaskNextAnnotate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "current_task", "next_task")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    current_task = sgqlc.types.Field(AnnotationTask, graphql_name="currentTask")
    next_task = sgqlc.types.Field(AnnotationTask, graphql_name="nextTask")


class AnnotationTaskNextReview(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "current_task", "next_task")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    current_task = sgqlc.types.Field(AnnotationTask, graphql_name="currentTask")
    next_task = sgqlc.types.Field(AnnotationTask, graphql_name="nextTask")


class AnnotationTaskSchedule(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "start_time",
        "interval",
        "sites",
        "rooms",
        "annotation_task_type_id",
        "annotation_task_type",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    interval = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="interval")
    sites = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Site")), graphql_name="sites"
    )
    rooms = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Room")), graphql_name="rooms"
    )
    annotation_task_type_id = sgqlc.types.Field(
        sgqlc.types.non_null(ID), graphql_name="annotationTaskTypeId"
    )
    annotation_task_type = sgqlc.types.Field(
        sgqlc.types.non_null("AnnotationTaskType"), graphql_name="annotationTaskType"
    )


class AnnotationTaskScheduleCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_annotation_task_schedule")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_annotation_task_schedule = sgqlc.types.Field(
        sgqlc.types.non_null(AnnotationTaskSchedule), graphql_name="createdAnnotationTaskSchedule"
    )


class AnnotationTaskScheduleUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_annotation_task_schedule")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    updated_annotation_task_schedule = sgqlc.types.Field(
        sgqlc.types.non_null(AnnotationTaskSchedule), graphql_name="updatedAnnotationTaskSchedule"
    )


class AnnotationTaskType(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "description",
        "event_types",
        "context_event_types",
        "archived_time",
        "schedules",
        "annotator_ids",
        "provisional_annotator_ids",
        "reviewer_ids",
        "priority",
        "detect_idle",
        "allow_skipping_review",
        "optimize_tasks",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="eventTypes"
    )
    context_event_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="contextEventTypes"
    )
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")
    schedules = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(AnnotationTaskSchedule)), graphql_name="schedules"
    )
    annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="annotatorIds"
    )
    provisional_annotator_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="provisionalAnnotatorIds"
    )
    reviewer_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="reviewerIds"
    )
    priority = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="priority")
    detect_idle = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="detectIdle")
    allow_skipping_review = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="allowSkippingReview"
    )
    optimize_tasks = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="optimizeTasks")


class AnnotationTaskUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_annotation_task")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_annotation_task = sgqlc.types.Field(
        AnnotationTask, graphql_name="updatedAnnotationTask"
    )


class AnnotationTasksCount(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "org_id",
        "site_id",
        "room_id",
        "annotator_user_id",
        "reviewer_user_id",
        "status",
    )
    org_id = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="orgId",
    )
    site_id = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="siteId",
    )
    room_id = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="roomId",
    )
    annotator_user_id = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="annotatorUserId",
    )
    reviewer_user_id = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="reviewerUserId",
    )
    status = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("FieldCount"))),
        graphql_name="status",
    )


class ApellaCase(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "case",
        "actual",
        "forecast",
        "case_forecast",
        "type",
        "status",
        "source",
        "start_time",
        "end_time",
        "room",
        "room_id",
        "site_id",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    case = sgqlc.types.Field("ScheduledCase", graphql_name="case")
    actual = sgqlc.types.Field("Phase", graphql_name="actual")
    forecast = sgqlc.types.Field("Phase", graphql_name="forecast")
    case_forecast = sgqlc.types.Field("CaseForecast", graphql_name="caseForecast")
    type = sgqlc.types.Field(sgqlc.types.non_null(CaseType), graphql_name="type")
    status = sgqlc.types.Field(
        sgqlc.types.non_null("ApellaCaseStatus"),
        graphql_name="status",
        args=sgqlc.types.ArgDict(
            (
                (
                    "use_observations",
                    sgqlc.types.Arg(Boolean, graphql_name="useObservations", default=None),
                ),
            )
        ),
    )
    source = sgqlc.types.Field(sgqlc.types.non_null(CaseSource), graphql_name="source")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(DateTime, graphql_name="endTime")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")


class ApellaCaseConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ApellaCaseEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ApellaCaseEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(ApellaCase), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class ApellaCaseStatus(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("name", "source", "since")
    name = sgqlc.types.Field(sgqlc.types.non_null(CaseStatusName), graphql_name="name")
    source = sgqlc.types.Field(sgqlc.types.non_null(CaseSource), graphql_name="source")
    since = sgqlc.types.Field(DateTime, graphql_name="since")


class AvailableTimeSlot(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "room_id",
        "start_time",
        "end_time",
        "block_time_ids",
        "max_available_duration",
    )
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    block_time_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="blockTimeIds",
    )
    max_available_duration = sgqlc.types.Field(
        sgqlc.types.non_null(Duration), graphql_name="maxAvailableDuration"
    )


class Block(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "color",
        "archived_time",
        "org_id",
        "site_ids",
        "organization",
        "block_times",
        "surgeons",
        "surgeon_ids",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    site_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="siteIds"
    )
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    block_times = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BlockTime"))),
        graphql_name="blockTimes",
    )
    surgeons = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("Staff"))),
        graphql_name="surgeons",
    )
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ID))),
        graphql_name="surgeonIds",
    )


class BlockArchive(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("block",)
    block = sgqlc.types.Field(Block, graphql_name="block")


class BlockConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BlockEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class BlockCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("block",)
    block = sgqlc.types.Field(Block, graphql_name="block")


class BlockEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Block), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class BlockReleaseFileTransform(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class BlockReleaseProcessDateRange(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class BlockReleaseReprocess(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class BlockTime(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "block_id",
        "start_time",
        "end_time",
        "room_id",
        "room",
        "block",
        "released_from",
        "created_time",
        "releases",
        "surgeon_ids",
        "available_intervals",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    block_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="blockId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    block = sgqlc.types.Field(sgqlc.types.non_null(Block), graphql_name="block")
    released_from = sgqlc.types.Field(String, graphql_name="releasedFrom")
    created_time = sgqlc.types.Field(DateTime, graphql_name="createdTime")
    releases = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeRelease"))),
        graphql_name="releases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "include_unreleased",
                    sgqlc.types.Arg(Boolean, graphql_name="includeUnreleased", default=False),
                ),
            )
        ),
    )
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="surgeonIds"
    )
    available_intervals = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeAvailableInterval")),
        graphql_name="availableIntervals",
    )


class BlockTimeAvailableInterval(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "block_time_id", "start_time", "end_time", "room_id", "surgeon_ids")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    block_time_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="blockTimeId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    surgeon_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(ID)), graphql_name="surgeonIds"
    )


class BlockTimeAvailableIntervalConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeAvailableIntervalEdge"))
        ),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class BlockTimeAvailableIntervalEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(BlockTimeAvailableInterval), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class BlockTimeBulkCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "ids")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ID))), graphql_name="ids"
    )


class BlockTimeBulkDeleteDuplicate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "deleted_count")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    deleted_count = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="deletedCount")


class BlockTimeConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BlockTimeEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class BlockTimeEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(BlockTime), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class BlockTimeRelease(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "block_time_id",
        "start_time",
        "end_time",
        "block_time",
        "reason",
        "released_at",
        "released_time",
        "source",
        "source_type",
        "unreleased_time",
        "unreleased_source",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    block_time_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="blockTimeId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    block_time = sgqlc.types.Field(sgqlc.types.non_null(BlockTime), graphql_name="blockTime")
    reason = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="reason")
    released_at = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="releasedAt")
    released_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="releasedTime")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    unreleased_time = sgqlc.types.Field(DateTime, graphql_name="unreleasedTime")
    unreleased_source = sgqlc.types.Field(String, graphql_name="unreleasedSource")


class BlockUnarchive(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("block",)
    block = sgqlc.types.Field(Block, graphql_name="block")


class BlockUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("block",)
    block = sgqlc.types.Field(Block, graphql_name="block")


class BlockUtilization(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "date",
        "block_id",
        "cases_for_block_day",
        "utilized_scheduled_seconds",
        "total_scheduled_case_seconds",
        "utilized_seconds",
        "total_case_seconds",
        "total_actual_case_seconds",
        "available_seconds",
        "total_block_seconds",
        "block",
    )
    date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="date")
    block_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="blockId")
    cases_for_block_day = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of("CaseToBlock")), graphql_name="casesForBlockDay"
    )
    utilized_scheduled_seconds = sgqlc.types.Field(
        sgqlc.types.non_null(Int), graphql_name="utilizedScheduledSeconds"
    )
    total_scheduled_case_seconds = sgqlc.types.Field(
        sgqlc.types.non_null(Int), graphql_name="totalScheduledCaseSeconds"
    )
    utilized_seconds = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="utilizedSeconds")
    total_case_seconds = sgqlc.types.Field(Int, graphql_name="totalCaseSeconds")
    total_actual_case_seconds = sgqlc.types.Field(
        sgqlc.types.non_null(Int), graphql_name="totalActualCaseSeconds"
    )
    available_seconds = sgqlc.types.Field(
        sgqlc.types.non_null(Int), graphql_name="availableSeconds"
    )
    total_block_seconds = sgqlc.types.Field(
        sgqlc.types.non_null(Int), graphql_name="totalBlockSeconds"
    )
    block = sgqlc.types.Field(sgqlc.types.non_null(Block), graphql_name="block")


class BoardConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "page_size",
        "page_duration",
        "blur_video",
        "updated_time",
        "updated_by_user",
        "site",
        "organization",
        "rooms",
        "enable_video",
        "board_view_type",
        "zoom_percent",
        "show_closed_rooms",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    page_size = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageSize")
    page_duration = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="pageDuration")
    blur_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="blurVideo")
    updated_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="updatedTime")
    updated_by_user = sgqlc.types.Field(sgqlc.types.non_null("User"), graphql_name="updatedByUser")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    rooms = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Room")), graphql_name="rooms"
    )
    enable_video = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="enableVideo")
    board_view_type = sgqlc.types.Field(
        sgqlc.types.non_null(BoardViewType), graphql_name="boardViewType"
    )
    zoom_percent = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="zoomPercent")
    show_closed_rooms = sgqlc.types.Field(Boolean, graphql_name="showClosedRooms")


class BoardConfigConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("BoardConfigEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class BoardConfigCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "board_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    board_config = sgqlc.types.Field(BoardConfig, graphql_name="boardConfig")


class BoardConfigDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class BoardConfigEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(BoardConfig), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class BoardConfigUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "board_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    board_config = sgqlc.types.Field(BoardConfig, graphql_name="boardConfig")


class Camera(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "organization_id",
        "organization",
        "site_id",
        "site",
        "room_id",
        "room",
        "latest_image",
        "labels",
        "rtsp_url",
        "family",
        "patient_bounding_box",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    latest_image = sgqlc.types.Field("CameraLatestImage", graphql_name="latestImage")
    labels = sgqlc.types.Field(JSON, graphql_name="labels")
    rtsp_url = sgqlc.types.Field(String, graphql_name="rtspUrl")
    family = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="family")
    patient_bounding_box = sgqlc.types.Field(
        "PatientBoundingBox", graphql_name="patientBoundingBox"
    )


class CameraCaptureLatestImage(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CameraConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CameraEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CameraCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_camera")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_camera = sgqlc.types.Field(Camera, graphql_name="createdCamera")


class CameraEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Camera), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CameraLatestImage(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "image_uri", "capture_time", "image_bytes", "camera")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    image_uri = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="imageUri")
    capture_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="captureTime")
    image_bytes = sgqlc.types.Field(String, graphql_name="imageBytes")
    camera = sgqlc.types.Field(sgqlc.types.non_null(Camera), graphql_name="camera")


class CameraLatestImageConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("CameraLatestImageEdge")), graphql_name="edges"
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CameraLatestImageEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(CameraLatestImage, graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CameraUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_camera")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_camera = sgqlc.types.Field(Camera, graphql_name="updatedCamera")


class CamerasCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_cameras")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_cameras = sgqlc.types.Field(sgqlc.types.list_of(Camera), graphql_name="createdCameras")


class CaseClassificationType(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "org_id", "name", "decription", "created_time", "updated_time")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    decription = sgqlc.types.Field(String, graphql_name="decription")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")


class CaseDurationPredictions(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("meta", "standard", "complex", "samples")
    meta = sgqlc.types.Field("PredictionMetadata", graphql_name="meta")
    standard = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="standard")
    complex = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="complex")
    samples = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(Float)), graphql_name="samples"
    )


class CaseDurationProcedureOption(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("procedure_name",)
    procedure_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="procedureName")


class CaseDurationProcedureOptionConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(sgqlc.types.non_null("CaseDurationProcedureOptionEdge"))
        ),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseDurationProcedureOptionEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseDurationProcedureOption), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseDurationSurgeonOption(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("surgeon_id", "surgeon_name")
    surgeon_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="surgeonId")
    surgeon_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="surgeonName")


class CaseDurationSurgeonOptionConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(sgqlc.types.non_null("CaseDurationSurgeonOptionEdge"))
        ),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseDurationSurgeonOptionEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseDurationSurgeonOption), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseDurationSurgeonProcedureMapping(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("surgeon_id", "surgeon", "procedure")
    surgeon_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="surgeonId")
    surgeon = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="surgeon")
    procedure = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="procedure")


class CaseDurationSurgeonProcedureMappingConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(sgqlc.types.non_null("CaseDurationSurgeonProcedureMappingEdge"))
        ),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseDurationSurgeonProcedureMappingEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(
        sgqlc.types.non_null(CaseDurationSurgeonProcedureMapping), graphql_name="node"
    )
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseDurationTurnoverPrediction(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("meta", "before_case", "after_case", "open_before_case", "clean_after_case")
    meta = sgqlc.types.Field("PredictionMetadata", graphql_name="meta")
    before_case = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="beforeCase")
    after_case = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="afterCase")
    open_before_case = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="openBeforeCase")
    clean_after_case = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="cleanAfterCase")


class CaseEhrMessage(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "message",
        "organization",
        "event_type",
        "event_time",
        "scheduled_case",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    message = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="message")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    event_type = sgqlc.types.Field(String, graphql_name="eventType")
    event_time = sgqlc.types.Field(DateTime, graphql_name="eventTime")
    scheduled_case = sgqlc.types.Field("ScheduledCase", graphql_name="scheduledCase")


class CaseEhrMessageConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseEhrMessageEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseEhrMessageEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseEhrMessage), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseFlag(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "flag_type",
        "case",
        "created_time",
        "updated_time",
        "archived_time",
        "organization",
        "site",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    flag_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="flagType")
    case = sgqlc.types.Field(sgqlc.types.non_null("ScheduledCase"), graphql_name="case")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")


class CaseFlagConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseFlagEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseFlagEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseFlag), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseFlagUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "case_flags")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    case_flags = sgqlc.types.Field(
        sgqlc.types.non_null(CaseFlagConnection),
        graphql_name="caseFlags",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class CaseForecast(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "case",
        "forecast_start_time",
        "forecast_end_time",
        "forecast_variant",
        "room",
        "site",
        "organization",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    case = sgqlc.types.Field(sgqlc.types.non_null("ScheduledCase"), graphql_name="case")
    forecast_start_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastStartTime"
    )
    forecast_end_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="forecastEndTime"
    )
    forecast_variant = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="forecastVariant"
    )
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )


class CaseForecastConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseForecastEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseForecastEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseForecast), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseForecastUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_forecasts")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_forecasts = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseForecast))),
        graphql_name="createdForecasts",
    )


class CaseLabel(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "field_id",
        "color",
        "abbreviation",
        "value",
        "boolean_value",
        "case_id",
        "option_id",
        "updated_by_user_id",
        "archived_time",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    field_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="fieldId")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    abbreviation = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="abbreviation")
    value = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="value")
    boolean_value = sgqlc.types.Field(Boolean, graphql_name="booleanValue")
    case_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="caseId")
    option_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="optionId")
    updated_by_user_id = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="updatedByUserId"
    )
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")


class CaseLabelCategory(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "fields")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    fields = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseLabelField"))),
        graphql_name="fields",
    )


class CaseLabelField(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "type", "ordinal", "options")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    type = sgqlc.types.Field(sgqlc.types.non_null(CaseLabelFieldType), graphql_name="type")
    ordinal = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="ordinal")
    options = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseLabelFieldOption"))),
        graphql_name="options",
    )


class CaseLabelFieldOption(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "field_id", "color", "abbreviation", "value", "boolean_value")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    field_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="fieldId")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    abbreviation = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="abbreviation")
    value = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="value")
    boolean_value = sgqlc.types.Field(Boolean, graphql_name="booleanValue")


class CaseLabelUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "case_labels")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    case_labels = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseLabel))),
        graphql_name="caseLabels",
    )


class CaseNotePlan(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "case", "note", "site", "organization", "created_time")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    case = sgqlc.types.Field(sgqlc.types.non_null("ScheduledCase"), graphql_name="case")
    note = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="note")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")


class CaseNotePlanUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("case_note_plan", "success")
    case_note_plan = sgqlc.types.Field(
        sgqlc.types.non_null(CaseNotePlan), graphql_name="caseNotePlan"
    )
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CaseProcedure(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("procedure", "hierarchy", "anesthesia")
    procedure = sgqlc.types.Field(sgqlc.types.non_null("Procedure"), graphql_name="procedure")
    hierarchy = sgqlc.types.Field(Int, graphql_name="hierarchy")
    anesthesia = sgqlc.types.Field(Anesthesia, graphql_name="anesthesia")


class CaseProceduresUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CaseProceduresUpsertAndArchive(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CaseStaff(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("staff", "role", "created_time")
    staff = sgqlc.types.Field(sgqlc.types.non_null("Staff"), graphql_name="staff")
    role = sgqlc.types.Field(String, graphql_name="role")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")


class CaseStaffPlan(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "staff",
        "case",
        "role",
        "site",
        "organization",
        "created_time",
        "archived_time",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    staff = sgqlc.types.Field("Staff", graphql_name="staff")
    case = sgqlc.types.Field("ScheduledCase", graphql_name="case")
    role = sgqlc.types.Field(String, graphql_name="role")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")


class CaseStaffPlanConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseStaffPlanEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseStaffPlanEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseStaffPlan), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseStaffPlanUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "case_staff_plans")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    case_staff_plans = sgqlc.types.Field(
        sgqlc.types.non_null(CaseStaffPlanConnection),
        graphql_name="caseStaffPlans",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class CaseStaffUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CaseStaffUpsertAndArchive(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CaseToBlock(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_id",
        "scheduled_case",
        "block_id",
        "override",
        "block_date",
        "score",
        "case_seconds",
        "utilized_case_seconds",
        "actual_case_seconds",
    )
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    scheduled_case = sgqlc.types.Field("ScheduledCase", graphql_name="scheduledCase")
    block_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="blockId")
    override = sgqlc.types.Field("CaseToBlockOverride", graphql_name="override")
    block_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="blockDate")
    score = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="score")
    case_seconds = sgqlc.types.Field(Int, graphql_name="caseSeconds")
    utilized_case_seconds = sgqlc.types.Field(Int, graphql_name="utilizedCaseSeconds")
    actual_case_seconds = sgqlc.types.Field(Int, graphql_name="actualCaseSeconds")


class CaseToBlockConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("CaseToBlockEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class CaseToBlockEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(CaseToBlock), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CaseToBlockOverride(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "case_id",
        "block_id",
        "block_date",
        "utilized_procedure_minutes",
        "utilized_turnover_minutes",
        "user_id",
        "note",
        "user",
    )
    case_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="caseId")
    block_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="blockId")
    block_date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="blockDate")
    utilized_procedure_minutes = sgqlc.types.Field(Int, graphql_name="utilizedProcedureMinutes")
    utilized_turnover_minutes = sgqlc.types.Field(Int, graphql_name="utilizedTurnoverMinutes")
    user_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="userId")
    note = sgqlc.types.Field(String, graphql_name="note")
    user = sgqlc.types.Field(sgqlc.types.non_null("User"), graphql_name="user")


class CaseToBlockOverridesUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class CheckNotificationsErrors(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "excess_notifications",
        "missing_notifications",
        "duplicate_notifications",
        "success",
    )
    excess_notifications = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("EventNotification"))),
        graphql_name="excessNotifications",
    )
    missing_notifications = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("MissingNotification"))),
        graphql_name="missingNotifications",
    )
    duplicate_notifications = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("EventNotification"))),
        graphql_name="duplicateNotifications",
    )
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class Cluster(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "enable_audio", "sites")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    enable_audio = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="enableAudio")
    sites = sgqlc.types.Field(
        sgqlc.types.non_null("SiteConnection"),
        graphql_name="sites",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class ClusterConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ClusterEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ClusterCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "cluster")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    cluster = sgqlc.types.Field(Cluster, graphql_name="cluster")


class ClusterEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Cluster), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class ClusterUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "cluster")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    cluster = sgqlc.types.Field(Cluster, graphql_name="cluster")


class ContactInformation(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "contact_information_value",
        "type",
        "initial_notification_sent",
        "first_name",
        "last_name",
        "staff_event_contact_information",
        "is_apella_employee",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    contact_information_value = sgqlc.types.Field(String, graphql_name="contactInformationValue")
    type = sgqlc.types.Field(ContactInformationType, graphql_name="type")
    initial_notification_sent = sgqlc.types.Field(DateTime, graphql_name="initialNotificationSent")
    first_name = sgqlc.types.Field(String, graphql_name="firstName")
    last_name = sgqlc.types.Field(String, graphql_name="lastName")
    staff_event_contact_information = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("StaffEventNotificationContactInformation")),
        graphql_name="staffEventContactInformation",
    )
    is_apella_employee = sgqlc.types.Field(Boolean, graphql_name="isApellaEmployee")


class ContactInformationConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ContactInformationEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ContactInformationEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(ContactInformation), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class CustomPhaseConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "start_event_type", "end_event_type", "name", "description")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    start_event_type = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="startEventType"
    )
    end_event_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="endEventType")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")


class CustomPhaseConfigDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "id")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    id = sgqlc.types.Field(ID, graphql_name="id")


class CustomPhaseConfigUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "custom_phase_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    custom_phase_config = sgqlc.types.Field(CustomPhaseConfig, graphql_name="customPhaseConfig")


class DefaultSiteClosuresCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_site_closures")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_site_closures = sgqlc.types.Field(
        sgqlc.types.list_of("SiteClosure"), graphql_name="createdSiteClosures"
    )


class EmailAvailableTimes(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class EmailAvailableTimesHtml(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "email", "subject")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    email = sgqlc.types.Field(String, graphql_name="email")
    subject = sgqlc.types.Field(String, graphql_name="subject")


class Event(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "type",
        "name",
        "label",
        "color",
        "description",
        "hidden",
        "start_time",
        "labels",
        "notes",
        "source",
        "source_type",
        "model_version",
        "confidence",
        "camera_id",
        "version",
        "deleted_at",
        "updated_time",
        "attrs",
        "organization",
        "site",
        "room",
        "etag",
        "event_matching_status",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    type = sgqlc.types.Field(String, graphql_name="type")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    label = sgqlc.types.Field(String, graphql_name="label")
    color = sgqlc.types.Field(String, graphql_name="color")
    description = sgqlc.types.Field(String, graphql_name="description")
    hidden = sgqlc.types.Field(Boolean, graphql_name="hidden")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labels"
    )
    notes = sgqlc.types.Field(String, graphql_name="notes")
    source = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="source")
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    model_version = sgqlc.types.Field(String, graphql_name="modelVersion")
    confidence = sgqlc.types.Field(Float, graphql_name="confidence")
    camera_id = sgqlc.types.Field(String, graphql_name="cameraId")
    version = sgqlc.types.Field(Int, graphql_name="version")
    deleted_at = sgqlc.types.Field(DateTime, graphql_name="deletedAt")
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")
    attrs = sgqlc.types.Field("EventType", graphql_name="attrs")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    etag = sgqlc.types.Field(String, graphql_name="etag")
    event_matching_status = sgqlc.types.Field(
        EventMatchingStatus, graphql_name="eventMatchingStatus"
    )


class EventConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("EventEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class EventCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_event")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_event = sgqlc.types.Field(Event, graphql_name="createdEvent")


class EventDashboardVisibility(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("event_type_id", "org_id_filter")
    event_type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="eventTypeId")
    org_id_filter = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="orgIdFilter"
    )


class EventDashboardVisibilityDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class EventDashboardVisibilityUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class EventDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class EventEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Event), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class EventLabelOption(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")


class EventLabelOptionCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("event_label_option",)
    event_label_option = sgqlc.types.Field(EventLabelOption, graphql_name="eventLabelOption")


class EventLabelOptionDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id",)
    id = sgqlc.types.Field(ID, graphql_name="id")


class EventLabelOptionUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("event_label_option",)
    event_label_option = sgqlc.types.Field(EventLabelOption, graphql_name="eventLabelOption")


class EventNotification(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "event",
        "observation",
        "message_id",
        "case",
        "event_time",
        "created_time",
        "sent_time",
        "staff_event_contact_information",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    event = sgqlc.types.Field(Event, graphql_name="event")
    observation = sgqlc.types.Field("Observation", graphql_name="observation")
    message_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="messageId")
    case = sgqlc.types.Field("ScheduledCase", graphql_name="case")
    event_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="eventTime")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    sent_time = sgqlc.types.Field(DateTime, graphql_name="sentTime")
    staff_event_contact_information = sgqlc.types.Field(
        sgqlc.types.non_null("StaffEventNotificationContactInformation"),
        graphql_name="staffEventContactInformation",
    )


class EventType(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "type", "name", "description", "color", "hidden")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="type")
    name = sgqlc.types.Field(String, graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    hidden = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="hidden")


class EventTypeConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("EventTypeEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class EventTypeCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_event_type")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_event_type = sgqlc.types.Field(
        sgqlc.types.non_null(EventType), graphql_name="createdEventType"
    )


class EventTypeEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(EventType), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class EventTypeUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_event_type")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    updated_event_type = sgqlc.types.Field(
        sgqlc.types.non_null(EventType), graphql_name="updatedEventType"
    )


class EventUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_event")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    updated_event = sgqlc.types.Field(Event, graphql_name="updatedEvent")


class EventUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_events")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_events = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(Event))),
        graphql_name="createdEvents",
    )


class FieldCount(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("parent_id", "count")
    parent_id = sgqlc.types.Field(ID, graphql_name="parentId")
    count = sgqlc.types.Field(Int, graphql_name="count")


class Highlight(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "description",
        "organization_id",
        "site_id",
        "room_id",
        "start_time",
        "end_time",
        "category",
        "archived_time",
        "feedback",
        "my_feedback",
        "users",
        "organization",
        "site",
        "room",
        "has_video_available",
        "duration",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    category = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="category")
    archived_time = sgqlc.types.Field(DateTime, graphql_name="archivedTime")
    feedback = sgqlc.types.Field(
        sgqlc.types.non_null("HighlightFeedbackConnection"),
        graphql_name="feedback",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    my_feedback = sgqlc.types.Field("HighlightFeedback", graphql_name="myFeedback")
    users = sgqlc.types.Field(
        sgqlc.types.non_null("UserConnection"),
        graphql_name="users",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    has_video_available = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="hasVideoAvailable"
    )
    duration = sgqlc.types.Field(Duration, graphql_name="duration")


class HighlightArchive(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "archived_highlight")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    archived_highlight = sgqlc.types.Field(Highlight, graphql_name="archivedHighlight")


class HighlightConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("HighlightEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class HighlightCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_highlight")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_highlight = sgqlc.types.Field(Highlight, graphql_name="createdHighlight")


class HighlightDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class HighlightEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Highlight), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class HighlightFeedback(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "comment",
        "rating",
        "submitted_date",
        "highlight_id",
        "highlight",
        "user_id",
        "user",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    comment = sgqlc.types.Field(String, graphql_name="comment")
    rating = sgqlc.types.Field(Int, graphql_name="rating")
    submitted_date = sgqlc.types.Field(DateTime, graphql_name="submittedDate")
    highlight_id = sgqlc.types.Field(String, graphql_name="highlightId")
    highlight = sgqlc.types.Field(sgqlc.types.non_null(Highlight), graphql_name="highlight")
    user_id = sgqlc.types.Field(String, graphql_name="userId")
    user = sgqlc.types.Field(sgqlc.types.non_null("User"), graphql_name="user")


class HighlightFeedbackConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("HighlightFeedbackEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class HighlightFeedbackCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_highlight_feedback")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_highlight_feedback = sgqlc.types.Field(
        HighlightFeedback, graphql_name="createdHighlightFeedback"
    )


class HighlightFeedbackEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(HighlightFeedback), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class HighlightFeedbackUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_highlight_feedback")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_highlight_feedback = sgqlc.types.Field(
        HighlightFeedback, graphql_name="updatedHighlightFeedback"
    )


class HighlightUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_highlight")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_highlight = sgqlc.types.Field(Highlight, graphql_name="updatedHighlight")


class MatchCases(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "phases")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    phases = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Phase")), graphql_name="phases"
    )


class MatchingStatusReason(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "explanation_for_change", "case_matching_status")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    explanation_for_change = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="explanationForChange"
    )
    case_matching_status = sgqlc.types.Field(
        sgqlc.types.non_null(CaseMatchingStatus), graphql_name="caseMatchingStatus"
    )


class MeasurementPeriod(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "measurement_period_start",
        "measurement_period_end",
        "site",
        "annotation_task_type",
        "room_ids",
        "days_of_week",
        "iso_days_of_week",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    measurement_period_start = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodStart"
    )
    measurement_period_end = sgqlc.types.Field(
        sgqlc.types.non_null(Date), graphql_name="measurementPeriodEnd"
    )
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    annotation_task_type = sgqlc.types.Field(
        sgqlc.types.non_null(AnnotationTaskType), graphql_name="annotationTaskType"
    )
    room_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ID))), graphql_name="roomIds"
    )
    days_of_week = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(DayOfWeek))),
        graphql_name="daysOfWeek",
    )
    iso_days_of_week = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(Int))),
        graphql_name="isoDaysOfWeek",
    )


class MeasurementPeriodConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("MeasurementPeriodEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class MeasurementPeriodDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class MeasurementPeriodEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(MeasurementPeriod), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class MeasurementPeriodUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("measurement_period", "success")
    measurement_period = sgqlc.types.Field(MeasurementPeriod, graphql_name="measurementPeriod")
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class MetricBucketFloat(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("date", "value", "bucket_size")
    date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="date")
    value = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="value")
    bucket_size = sgqlc.types.Field(sgqlc.types.non_null(Duration), graphql_name="bucketSize")


class MetricFloat(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("metric", "value")
    metric = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="metric")
    value = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="value")


class MissingNotification(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("event", "subscriber_information", "case")
    event = sgqlc.types.Field(sgqlc.types.non_null(Event), graphql_name="event")
    subscriber_information = sgqlc.types.Field(
        sgqlc.types.non_null("StaffEventNotificationContactInformation"),
        graphql_name="subscriberInformation",
    )
    case = sgqlc.types.Field("ScheduledCase", graphql_name="case")


class MultiMetricBucketFloat(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("date", "bucket_size", "metrics")
    date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="date")
    bucket_size = sgqlc.types.Field(sgqlc.types.non_null(Duration), graphql_name="bucketSize")
    metrics = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(MetricFloat))),
        graphql_name="metrics",
    )


class Mutation(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "event_create",
        "event_update",
        "event_delete",
        "event_upsert",
        "event_type_create",
        "event_type_update",
        "event_label_option_create",
        "event_label_option_update",
        "event_label_option_delete",
        "highlight_create",
        "highlight_update",
        "highlight_delete",
        "highlight_archive",
        "highlight_feedback_create",
        "highlight_feedback_update",
        "annotation_task_bulk_generate",
        "annotation_task_bulk_update",
        "annotation_task_update",
        "annotation_task_exit",
        "annotation_task_type_create",
        "annotation_task_type_update",
        "annotation_task_schedule_create",
        "annotation_task_schedule_update",
        "annotation_task_next_annotate",
        "annotation_task_next_review",
        "camera_capture_latest_image",
        "case_procedures_upsert",
        "process_case_derived_properties",
        "case_note_plan_upsert",
        "case_procedures_upsert_and_archive",
        "case_staff_upsert",
        "case_staff_upsert_and_archive",
        "case_staff_plan_upsert",
        "case_flag_upsert",
        "match_cases",
        "procedures_upsert",
        "anesthesias_upsert",
        "measurement_period_delete",
        "measurement_period_upsert",
        "staff_upsert",
        "staff_codes_upsert",
        "phase_delete",
        "phase_update",
        "phase_create",
        "phase_upsert",
        "phase_relationship_delete",
        "phase_relationship_create",
        "observation_create",
        "observation_delete",
        "observations_upsert",
        "subscribers_upsert",
        "staffing_needs_ratio_create",
        "notify_staff_for_events",
        "check_notifications_errors",
        "email_available_times",
        "email_available_times_html",
        "block_create",
        "block_update",
        "block_archive",
        "block_unarchive",
        "block_time_bulk_create",
        "block_releases_reprocess",
        "block_release_process_date_range",
        "block_release_file_transform",
        "block_time_bulk_delete_duplicate",
        "board_config_create",
        "board_config_update",
        "board_config_delete",
        "organization_create",
        "organization_update",
        "site_create",
        "site_update",
        "site_prime_time_config_upsert",
        "site_closure_create",
        "site_closure_delete",
        "default_site_closures_create",
        "site_first_case_config_upsert",
        "site_launches_upsert",
        "room_create",
        "rooms_create",
        "room_update",
        "room_update_configuration",
        "room_tag_create",
        "room_set_tags",
        "room_tag_rename",
        "room_tag_update",
        "room_closure_create",
        "room_closure_delete",
        "room_prime_time_config_upsert",
        "room_prime_time_config_delete",
        "room_first_case_config_upsert",
        "room_first_case_config_delete",
        "camera_create",
        "cameras_create",
        "camera_update",
        "user_filter_view_create",
        "user_filter_view_update",
        "user_filter_view_delete",
        "cluster_create",
        "cluster_update",
        "turnover_goals_update",
        "case_forecast_upsert",
        "upsert_forecasts_for_cases",
        "event_dashboard_visibility_upsert",
        "event_dashboard_visibility_delete",
        "terminal_clean_score_upsert",
        "turnover_label_note_upsert",
        "case_to_block_overrides_upsert",
        "custom_phase_config_upsert",
        "custom_phase_config_delete",
        "case_label_upsert",
    )
    event_create = sgqlc.types.Field(
        EventCreate,
        graphql_name="eventCreate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(EventCreateInput, graphql_name="input", default=None)),)
        ),
    )
    event_update = sgqlc.types.Field(
        EventUpdate,
        graphql_name="eventUpdate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(EventUpdateInput, graphql_name="input", default=None)),)
        ),
    )
    event_delete = sgqlc.types.Field(
        EventDelete,
        graphql_name="eventDelete",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(EventDeleteInput, graphql_name="input", default=None)),)
        ),
    )
    event_upsert = sgqlc.types.Field(
        EventUpsert,
        graphql_name="eventUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(EventUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_type_create = sgqlc.types.Field(
        EventTypeCreate,
        graphql_name="eventTypeCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventTypeCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_type_update = sgqlc.types.Field(
        EventTypeUpdate,
        graphql_name="eventTypeUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventTypeUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_label_option_create = sgqlc.types.Field(
        EventLabelOptionCreate,
        graphql_name="eventLabelOptionCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventLabelOptionCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_label_option_update = sgqlc.types.Field(
        EventLabelOptionUpdate,
        graphql_name="eventLabelOptionUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventLabelOptionUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_label_option_delete = sgqlc.types.Field(
        EventLabelOptionDelete,
        graphql_name="eventLabelOptionDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventLabelOptionDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    highlight_create = sgqlc.types.Field(
        HighlightCreate,
        graphql_name="highlightCreate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(HighlightCreateInput, graphql_name="input", default=None)),)
        ),
    )
    highlight_update = sgqlc.types.Field(
        HighlightUpdate,
        graphql_name="highlightUpdate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(HighlightUpdateInput, graphql_name="input", default=None)),)
        ),
    )
    highlight_delete = sgqlc.types.Field(
        HighlightDelete,
        graphql_name="highlightDelete",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(HighlightDeleteInput, graphql_name="input", default=None)),)
        ),
    )
    highlight_archive = sgqlc.types.Field(
        HighlightArchive,
        graphql_name="highlightArchive",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(HighlightArchiveInput, graphql_name="input", default=None)),)
        ),
    )
    highlight_feedback_create = sgqlc.types.Field(
        HighlightFeedbackCreate,
        graphql_name="highlightFeedbackCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        HighlightFeedbackCreateInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    highlight_feedback_update = sgqlc.types.Field(
        HighlightFeedbackUpdate,
        graphql_name="highlightFeedbackUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        HighlightFeedbackUpdateInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    annotation_task_bulk_generate = sgqlc.types.Field(
        AnnotationTaskBulkGenerate,
        graphql_name="annotationTaskBulkGenerate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        AnnotationTaskBulkGenerateInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    annotation_task_bulk_update = sgqlc.types.Field(
        AnnotationTaskBulkUpdate,
        graphql_name="annotationTaskBulkUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        AnnotationTaskBulkUpdateInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    annotation_task_update = sgqlc.types.Field(
        AnnotationTaskUpdate,
        graphql_name="annotationTaskUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(AnnotationTaskUpdateInput, graphql_name="input", default=None),
                ),
            )
        ),
    )
    annotation_task_exit = sgqlc.types.Field(
        AnnotationTaskExit,
        graphql_name="annotationTaskExit",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskExitInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_type_create = sgqlc.types.Field(
        AnnnotationTaskTypeCreate,
        graphql_name="annotationTaskTypeCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnnotationTaskTypeCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_type_update = sgqlc.types.Field(
        AnnnotationTaskTypeUpdate,
        graphql_name="annotationTaskTypeUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnnotationTaskTypeUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_schedule_create = sgqlc.types.Field(
        AnnotationTaskScheduleCreate,
        graphql_name="annotationTaskScheduleCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskScheduleCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_schedule_update = sgqlc.types.Field(
        AnnotationTaskScheduleUpdate,
        graphql_name="annotationTaskScheduleUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskScheduleUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_next_annotate = sgqlc.types.Field(
        AnnotationTaskNextAnnotate,
        graphql_name="annotationTaskNextAnnotate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskNextAnnotateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    annotation_task_next_review = sgqlc.types.Field(
        AnnotationTaskNextReview,
        graphql_name="annotationTaskNextReview",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskNextReviewInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    camera_capture_latest_image = sgqlc.types.Field(
        CameraCaptureLatestImage,
        graphql_name="cameraCaptureLatestImage",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        CameraCaptureLatestImageInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    case_procedures_upsert = sgqlc.types.Field(
        CaseProceduresUpsert,
        graphql_name="caseProceduresUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseProceduresUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    process_case_derived_properties = sgqlc.types.Field(
        "ProcessCaseDerivedProperties",
        graphql_name="processCaseDerivedProperties",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(
                                sgqlc.types.non_null(ProcessCaseDerivedPropertiesInput)
                            )
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_note_plan_upsert = sgqlc.types.Field(
        CaseNotePlanUpsert,
        graphql_name="caseNotePlanUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseNotePlanUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_procedures_upsert_and_archive = sgqlc.types.Field(
        CaseProceduresUpsertAndArchive,
        graphql_name="caseProceduresUpsertAndArchive",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(
                                sgqlc.types.non_null(CaseProceduresUpsertAndArchiveInput)
                            )
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_staff_upsert = sgqlc.types.Field(
        CaseStaffUpsert,
        graphql_name="caseStaffUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseStaffUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_staff_upsert_and_archive = sgqlc.types.Field(
        CaseStaffUpsertAndArchive,
        graphql_name="caseStaffUpsertAndArchive",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(
                                sgqlc.types.non_null(CaseStaffUpsertAndArchiveInput)
                            )
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_staff_plan_upsert = sgqlc.types.Field(
        CaseStaffPlanUpsert,
        graphql_name="caseStaffPlanUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseStaffPlanUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_flag_upsert = sgqlc.types.Field(
        sgqlc.types.non_null(CaseFlagUpsert),
        graphql_name="caseFlagUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseFlagUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    match_cases = sgqlc.types.Field(
        MatchCases,
        graphql_name="matchCases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(MatchCaseInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    procedures_upsert = sgqlc.types.Field(
        "ProceduresUpsert",
        graphql_name="proceduresUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "procedures",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(ProcedureUpsertInput))
                        ),
                        graphql_name="procedures",
                        default=None,
                    ),
                ),
            )
        ),
    )
    anesthesias_upsert = sgqlc.types.Field(
        AnesthesiasUpsert,
        graphql_name="anesthesiasUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(AnesthesiaUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    measurement_period_delete = sgqlc.types.Field(
        MeasurementPeriodDelete,
        graphql_name="measurementPeriodDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        MeasurementPeriodDeleteInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    measurement_period_upsert = sgqlc.types.Field(
        MeasurementPeriodUpsert,
        graphql_name="measurementPeriodUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "measurement_period",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(MeasurementPeriodUpsertInput),
                        graphql_name="measurementPeriod",
                        default=None,
                    ),
                ),
            )
        ),
    )
    staff_upsert = sgqlc.types.Field(
        "StaffUpsert",
        graphql_name="staffUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "staff",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(StaffUpsertInput))
                        ),
                        graphql_name="staff",
                        default=None,
                    ),
                ),
            )
        ),
    )
    staff_codes_upsert = sgqlc.types.Field(
        "StaffCodesUpsert",
        graphql_name="staffCodesUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "staff_codes",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(StaffCodeUpsertInput))
                        ),
                        graphql_name="staffCodes",
                        default=None,
                    ),
                ),
            )
        ),
    )
    phase_delete = sgqlc.types.Field(
        "PhaseDelete",
        graphql_name="phaseDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseDeleteInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    phase_update = sgqlc.types.Field(
        "PhaseUpdate",
        graphql_name="phaseUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseUpdateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    phase_create = sgqlc.types.Field(
        "PhaseCreate",
        graphql_name="phaseCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    phase_upsert = sgqlc.types.Field(
        "PhaseUpsert",
        graphql_name="phaseUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(PhaseUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    phase_relationship_delete = sgqlc.types.Field(
        "PhaseRelationshipDelete",
        graphql_name="phaseRelationshipDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseRelationshipDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    phase_relationship_create = sgqlc.types.Field(
        "PhaseRelationshipCreate",
        graphql_name="phaseRelationshipCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseRelationshipCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    observation_create = sgqlc.types.Field(
        "ObservationCreate",
        graphql_name="observationCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(ObservationCreateInput, graphql_name="input", default=None),
                ),
            )
        ),
    )
    observation_delete = sgqlc.types.Field(
        "ObservationDelete",
        graphql_name="observationDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(ObservationDeleteInput, graphql_name="input", default=None),
                ),
            )
        ),
    )
    observations_upsert = sgqlc.types.Field(
        "ObservationsUpsert",
        graphql_name="observationsUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "observations",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(ObservationUpsertInput))
                        ),
                        graphql_name="observations",
                        default=None,
                    ),
                ),
            )
        ),
    )
    subscribers_upsert = sgqlc.types.Field(
        "SubscribersUpsert",
        graphql_name="subscribersUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(SubscriberUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    staffing_needs_ratio_create = sgqlc.types.Field(
        "StaffingNeedsRatioCreate",
        graphql_name="staffingNeedsRatioCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        StaffingNeedsRatioCreateInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    notify_staff_for_events = sgqlc.types.Field(
        "NotifyStaffForEvents",
        graphql_name="notifyStaffForEvents",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(StaffEventsNotificationsInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    check_notifications_errors = sgqlc.types.Field(
        CheckNotificationsErrors,
        graphql_name="checkNotificationsErrors",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(StaffEventsNotificationsInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    email_available_times = sgqlc.types.Field(
        EmailAvailableTimes,
        graphql_name="emailAvailableTimes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EmailAvailableTimesInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    email_available_times_html = sgqlc.types.Field(
        EmailAvailableTimesHtml,
        graphql_name="emailAvailableTimesHtml",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EmailAvailableTimesHtmlInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_create = sgqlc.types.Field(
        BlockCreate,
        graphql_name="blockCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    block_update = sgqlc.types.Field(
        BlockUpdate,
        graphql_name="blockUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockUpdateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    block_archive = sgqlc.types.Field(
        BlockArchive,
        graphql_name="blockArchive",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    block_unarchive = sgqlc.types.Field(
        BlockUnarchive,
        graphql_name="blockUnarchive",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    block_time_bulk_create = sgqlc.types.Field(
        BlockTimeBulkCreate,
        graphql_name="blockTimeBulkCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimeBulkCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_releases_reprocess = sgqlc.types.Field(
        BlockReleaseReprocess,
        graphql_name="blockReleasesReprocess",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockReleaseReprocessInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_release_process_date_range = sgqlc.types.Field(
        BlockReleaseProcessDateRange,
        graphql_name="blockReleaseProcessDateRange",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockReleaseProcessDateRangeInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_release_file_transform = sgqlc.types.Field(
        BlockReleaseFileTransform,
        graphql_name="blockReleaseFileTransform",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockReleaseReprocessInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_time_bulk_delete_duplicate = sgqlc.types.Field(
        BlockTimeBulkDeleteDuplicate,
        graphql_name="blockTimeBulkDeleteDuplicate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimeBulkDeleteDuplicateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    board_config_create = sgqlc.types.Field(
        BoardConfigCreate,
        graphql_name="boardConfigCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BoardConfigCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    board_config_update = sgqlc.types.Field(
        BoardConfigUpdate,
        graphql_name="boardConfigUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BoardConfigUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    board_config_delete = sgqlc.types.Field(
        BoardConfigDelete,
        graphql_name="boardConfigDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BoardConfigDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    organization_create = sgqlc.types.Field(
        "OrganizationCreate",
        graphql_name="organizationCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(OrganizationCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    organization_update = sgqlc.types.Field(
        "OrganizationUpdate",
        graphql_name="organizationUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(OrganizationUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    site_create = sgqlc.types.Field(
        "SiteCreate",
        graphql_name="siteCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    site_update = sgqlc.types.Field(
        "SiteUpdate",
        graphql_name="siteUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteUpdateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    site_prime_time_config_upsert = sgqlc.types.Field(
        "SitePrimeTimeConfigUpsert",
        graphql_name="sitePrimeTimeConfigUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SitePrimeTimeConfigUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    site_closure_create = sgqlc.types.Field(
        "SiteClosureCreate",
        graphql_name="siteClosureCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteClosureCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    site_closure_delete = sgqlc.types.Field(
        "SiteClosureDelete",
        graphql_name="siteClosureDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteClosureDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    default_site_closures_create = sgqlc.types.Field(
        DefaultSiteClosuresCreate,
        graphql_name="defaultSiteClosuresCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(DefaultSiteClosuresCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    site_first_case_config_upsert = sgqlc.types.Field(
        "SiteFirstCaseConfigUpsert",
        graphql_name="siteFirstCaseConfigUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteFirstCaseConfigUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    site_launches_upsert = sgqlc.types.Field(
        "SiteLaunchesUpsert",
        graphql_name="siteLaunchesUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(SiteLaunchUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_create = sgqlc.types.Field(
        "RoomCreate",
        graphql_name="roomCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    rooms_create = sgqlc.types.Field(
        "RoomsCreate",
        graphql_name="roomsCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(RoomCreateInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_update = sgqlc.types.Field(
        "RoomUpdate",
        graphql_name="roomUpdate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(RoomUpdateInput, graphql_name="input", default=None)),)
        ),
    )
    room_update_configuration = sgqlc.types.Field(
        "RoomUpdateConfiguration",
        graphql_name="roomUpdateConfiguration",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        RoomUpdateConfigurationInput, graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    room_tag_create = sgqlc.types.Field(
        "RoomTagCreate",
        graphql_name="roomTagCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomTagCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    room_set_tags = sgqlc.types.Field(
        "RoomSetTags",
        graphql_name="roomSetTags",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomSetTagsInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    room_tag_rename = sgqlc.types.Field(
        "RoomTagRename",
        graphql_name="roomTagRename",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomTagRenameInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    room_tag_update = sgqlc.types.Field(
        "RoomTagUpdate",
        graphql_name="roomTagUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomTagUpdateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    room_closure_create = sgqlc.types.Field(
        "RoomClosureCreate",
        graphql_name="roomClosureCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomClosureCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_closure_delete = sgqlc.types.Field(
        "RoomClosureDelete",
        graphql_name="roomClosureDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomClosureDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_prime_time_config_upsert = sgqlc.types.Field(
        "RoomPrimeTimeConfigUpsert",
        graphql_name="roomPrimeTimeConfigUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomPrimeTimeConfigUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_prime_time_config_delete = sgqlc.types.Field(
        "RoomPrimeTimeConfigDelete",
        graphql_name="roomPrimeTimeConfigDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomPrimeTimeConfigDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_first_case_config_upsert = sgqlc.types.Field(
        "RoomFirstCaseConfigUpsert",
        graphql_name="roomFirstCaseConfigUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomFirstCaseConfigUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    room_first_case_config_delete = sgqlc.types.Field(
        "RoomFirstCaseConfigDelete",
        graphql_name="roomFirstCaseConfigDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomFirstCaseConfigDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    camera_create = sgqlc.types.Field(
        CameraCreate,
        graphql_name="cameraCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CameraCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    cameras_create = sgqlc.types.Field(
        CamerasCreate,
        graphql_name="camerasCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CameraCreateInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    camera_update = sgqlc.types.Field(
        CameraUpdate,
        graphql_name="cameraUpdate",
        args=sgqlc.types.ArgDict(
            (("input", sgqlc.types.Arg(CameraUpdateInput, graphql_name="input", default=None)),)
        ),
    )
    user_filter_view_create = sgqlc.types.Field(
        "UserFilterViewCreate",
        graphql_name="userFilterViewCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(UserFilterViewCreateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    user_filter_view_update = sgqlc.types.Field(
        "UserFilterViewUpdate",
        graphql_name="userFilterViewUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(UserFilterViewUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    user_filter_view_delete = sgqlc.types.Field(
        "UserFilterViewDelete",
        graphql_name="userFilterViewDelete",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    cluster_create = sgqlc.types.Field(
        ClusterCreate,
        graphql_name="clusterCreate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ClusterCreateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    cluster_update = sgqlc.types.Field(
        ClusterUpdate,
        graphql_name="clusterUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ClusterUpdateInput), graphql_name="input", default=None
                    ),
                ),
            )
        ),
    )
    turnover_goals_update = sgqlc.types.Field(
        "TurnoverGoalsUpdate",
        graphql_name="turnoverGoalsUpdate",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(TurnoverGoalsUpdateInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_forecast_upsert = sgqlc.types.Field(
        CaseForecastUpsert,
        graphql_name="caseForecastUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseForecastUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    upsert_forecasts_for_cases = sgqlc.types.Field(
        "UpsertForecastsForCases",
        graphql_name="upsertForecastsForCases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseForecastForCaseInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
                ("temporality", sgqlc.types.Arg(String, graphql_name="temporality", default=None)),
            )
        ),
    )
    event_dashboard_visibility_upsert = sgqlc.types.Field(
        EventDashboardVisibilityUpsert,
        graphql_name="eventDashboardVisibilityUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventDashboardVisibilityUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    event_dashboard_visibility_delete = sgqlc.types.Field(
        EventDashboardVisibilityDelete,
        graphql_name="eventDashboardVisibilityDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventDashboardVisibilityDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    terminal_clean_score_upsert = sgqlc.types.Field(
        "TerminalCleanScoreUpsert",
        graphql_name="terminalCleanScoreUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(TerminalCleanScoreUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    turnover_label_note_upsert = sgqlc.types.Field(
        "TurnoverLabelNoteUpsert",
        graphql_name="turnoverLabelNoteUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(TurnoverLabelNoteUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_to_block_overrides_upsert = sgqlc.types.Field(
        CaseToBlockOverridesUpsert,
        graphql_name="caseToBlockOverridesUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(
                                sgqlc.types.non_null(CaseToBlockOverrideUpsertInput)
                            )
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    custom_phase_config_upsert = sgqlc.types.Field(
        CustomPhaseConfigUpsert,
        graphql_name="customPhaseConfigUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CustomPhaseConfigUpsertInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    custom_phase_config_delete = sgqlc.types.Field(
        CustomPhaseConfigDelete,
        graphql_name="customPhaseConfigDelete",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CustomPhaseConfigDeleteInput),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_label_upsert = sgqlc.types.Field(
        CaseLabelUpsert,
        graphql_name="caseLabelUpsert",
        args=sgqlc.types.ArgDict(
            (
                (
                    "input",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(
                            sgqlc.types.list_of(sgqlc.types.non_null(CaseLabelUpsertInput))
                        ),
                        graphql_name="input",
                        default=None,
                    ),
                ),
            )
        ),
    )


class NotifyStaffForEvents(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "sent_count", "failed_event_ids")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    sent_count = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="sentCount")
    failed_event_ids = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="failedEventIds",
    )


class ObjectMetrics(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("count_occupancy_per_bucket", "count_occupancy_and_outage_per_bucket")
    count_occupancy_per_bucket = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(MetricBucketFloat)),
        graphql_name="countOccupancyPerBucket",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(OccupancyBucketInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    count_occupancy_and_outage_per_bucket = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(MultiMetricBucketFloat)),
        graphql_name="countOccupancyAndOutagePerBucket",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(OccupancyBucketInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )


class Observation(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "observation_time",
        "recorded_time",
        "organization",
        "case",
        "type",
        "observation_type",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    observation_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="observationTime"
    )
    recorded_time = sgqlc.types.Field(DateTime, graphql_name="recordedTime")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null("Organization"), graphql_name="organization"
    )
    case = sgqlc.types.Field(sgqlc.types.non_null("ScheduledCase"), graphql_name="case")
    type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="type")
    observation_type = sgqlc.types.Field(
        sgqlc.types.non_null("ObservationType"), graphql_name="observationType"
    )


class ObservationConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ObservationEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ObservationCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_observation")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_observation = sgqlc.types.Field(Observation, graphql_name="createdObservation")


class ObservationDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class ObservationEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Observation), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class ObservationType(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "description", "color", "created_time", "updated_time")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    description = sgqlc.types.Field(String, graphql_name="description")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")


class ObservationTypeName(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("type_id", "name", "color")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="typeId")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(String, graphql_name="color")


class ObservationsUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_observations")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_observations = sgqlc.types.Field(
        sgqlc.types.non_null(ObservationConnection),
        graphql_name="createdObservations",
        args=sgqlc.types.ArgDict(
            (
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class Organization(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "auth0_org_id", "sites", "users", "event_types")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    auth0_org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="auth0OrgId")
    sites = sgqlc.types.Field(
        sgqlc.types.non_null("SiteConnection"),
        graphql_name="sites",
        args=sgqlc.types.ArgDict(
            (
                (
                    "site_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="siteIds",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    users = sgqlc.types.Field(
        sgqlc.types.non_null("UserConnection"),
        graphql_name="users",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    event_types = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(String))),
        graphql_name="eventTypes",
    )


class OrganizationConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null("PageInfo"), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("OrganizationEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field("PageCursors", graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class OrganizationCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_organization")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_organization = sgqlc.types.Field(Organization, graphql_name="createdOrganization")


class OrganizationEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Organization), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class OrganizationUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_organization")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_organization = sgqlc.types.Field(Organization, graphql_name="updatedOrganization")


class PageCursor(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("cursor", "is_current", "page")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")
    is_current = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="isCurrent")
    page = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="page")


class PageCursors(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("around", "first", "last", "next", "previous")
    around = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(PageCursor)), graphql_name="around"
    )
    first = sgqlc.types.Field(PageCursor, graphql_name="first")
    last = sgqlc.types.Field(PageCursor, graphql_name="last")
    next = sgqlc.types.Field(PageCursor, graphql_name="next")
    previous = sgqlc.types.Field(PageCursor, graphql_name="previous")


class PageInfo(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("has_next_page", "has_previous_page", "start_cursor", "end_cursor")
    has_next_page = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="hasNextPage")
    has_previous_page = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="hasPreviousPage"
    )
    start_cursor = sgqlc.types.Field(String, graphql_name="startCursor")
    end_cursor = sgqlc.types.Field(String, graphql_name="endCursor")


class Patient(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "personal_info")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    personal_info = sgqlc.types.Field("PersonalInfo", graphql_name="personalInfo")


class PatientBoundingBox(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("camera_id", "left_pct", "bottom_pct", "width_pct", "height_pct")
    camera_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cameraId")
    left_pct = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="leftPct")
    bottom_pct = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="bottomPct")
    width_pct = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="widthPct")
    height_pct = sgqlc.types.Field(sgqlc.types.non_null(Float), graphql_name="heightPct")


class PersonalInfo(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "first_name_abbreviated",
        "last_name_abbreviated",
        "fullname",
        "age_range",
        "age",
        "administrative_sex",
    )
    first_name_abbreviated = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="firstNameAbbreviated"
    )
    last_name_abbreviated = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="lastNameAbbreviated"
    )
    fullname = sgqlc.types.Field(String, graphql_name="fullname")
    age_range = sgqlc.types.Field(AgeRange, graphql_name="ageRange")
    age = sgqlc.types.Field(Int, graphql_name="age")
    administrative_sex = sgqlc.types.Field(AdministrativeSex, graphql_name="administrativeSex")


class Phase(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "organization",
        "site",
        "room",
        "type_id",
        "phase_detail",
        "source_type",
        "case",
        "child_phases",
        "parent_phases",
        "start_event",
        "end_event",
        "duration",
        "has_video_available",
        "start_time",
        "end_time",
        "created_time",
        "updated_time",
        "status",
        "invalidation_reason",
        "time_range_verified",
        "etag",
        "event_matching_status",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room = sgqlc.types.Field(sgqlc.types.non_null("Room"), graphql_name="room")
    type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="typeId")
    phase_detail = sgqlc.types.Field(
        sgqlc.types.non_null("PhaseTypeRecord"), graphql_name="phaseDetail"
    )
    source_type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="sourceType")
    case = sgqlc.types.Field("ScheduledCase", graphql_name="case")
    child_phases = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Phase")), graphql_name="childPhases"
    )
    parent_phases = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("Phase")), graphql_name="parentPhases"
    )
    start_event = sgqlc.types.Field(sgqlc.types.non_null(Event), graphql_name="startEvent")
    end_event = sgqlc.types.Field(Event, graphql_name="endEvent")
    duration = sgqlc.types.Field(Duration, graphql_name="duration")
    has_video_available = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="hasVideoAvailable"
    )
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(DateTime, graphql_name="endTime")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")
    status = sgqlc.types.Field(sgqlc.types.non_null(PhaseStatus), graphql_name="status")
    invalidation_reason = sgqlc.types.Field(String, graphql_name="invalidationReason")
    time_range_verified = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="timeRangeVerified"
    )
    etag = sgqlc.types.Field(String, graphql_name="etag")
    event_matching_status = sgqlc.types.Field(
        EventMatchingStatus, graphql_name="eventMatchingStatus"
    )


class PhaseConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("PhaseEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class PhaseCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_phase")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_phase = sgqlc.types.Field(sgqlc.types.non_null(Phase), graphql_name="createdPhase")


class PhaseDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class PhaseEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Phase), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class PhaseRelationshipCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class PhaseRelationshipDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class PhaseTypeConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("PhaseTypeEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class PhaseTypeEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null("PhaseTypeRecord"), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class PhaseTypeRecord(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("type", "title", "slug", "description")
    type = sgqlc.types.Field(sgqlc.types.non_null(PhaseType), graphql_name="type")
    title = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="title")
    slug = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="slug")
    description = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="description")


class PhaseUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")


class PhaseUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_phases")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_phases = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(Phase))),
        graphql_name="createdPhases",
    )


class PredictionMetadata(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("surgeon_name", "procedure_name", "additional_procedures")
    surgeon_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="surgeonName")
    procedure_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="procedureName")
    additional_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(String)), graphql_name="additionalProcedures"
    )


class Procedure(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "organization", "name", "hierarchy")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    hierarchy = sgqlc.types.Field(Int, graphql_name="hierarchy")


class ProcedureConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ProcedureEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ProcedureEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Procedure), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class ProceduresUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_procedures")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(ProcedureConnection),
        graphql_name="createdProcedures",
        args=sgqlc.types.ArgDict(
            (
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class ProcessCaseDerivedProperties(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class Query(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "me",
        "user",
        "users",
        "organization",
        "organizations",
        "site",
        "sites",
        "room",
        "rooms",
        "room_closures",
        "site_closures",
        "room_tags",
        "camera",
        "cameras",
        "cluster",
        "clusters",
        "event",
        "event_search",
        "event_history_search",
        "event_label_options",
        "event_type",
        "event_types",
        "highlight",
        "highlights",
        "highlight_search",
        "highlight_feedback_search",
        "annotation_task",
        "annotation_tasks",
        "annotation_task_type",
        "annotation_task_types",
        "phases",
        "apella_cases",
        "phase_type",
        "phase_types",
        "object_metrics",
        "scheduled_cases",
        "cases",
        "cases_history",
        "case_ehr_messages",
        "case_classification_types",
        "live_camera_images",
        "service_lines",
        "procedures",
        "anesthesias",
        "measurement_period",
        "measurement_periods",
        "staff",
        "observation",
        "observation_type_names",
        "observation_type_names_for_custom_phases",
        "contact_information",
        "staff_event_notification_contact_information",
        "staffing_needs_roles",
        "block",
        "blocks",
        "block_time",
        "block_times_bulk",
        "block_times",
        "block_times_available_intervals",
        "board_configs",
        "case_duration_surgeons_and_procedures",
        "case_duration_turnover_prediction",
        "case_duration_predictions",
        "case_duration_surgeons",
        "case_duration_procedures",
        "available_time_slots",
        "user_filter_views",
        "case_forecasts",
        "dashboard_events",
        "turnover_labels",
        "cases_to_blocks",
        "case_to_block_overrides",
        "block_utilizations",
        "custom_phase_configs",
        "terminal_clean_score",
    )
    me = sgqlc.types.Field("User", graphql_name="me")
    user = sgqlc.types.Field(
        "User",
        graphql_name="user",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    users = sgqlc.types.Field(
        sgqlc.types.non_null("UserConnection"),
        graphql_name="users",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(UsersSearchInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    organization = sgqlc.types.Field(
        Organization,
        graphql_name="organization",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    organizations = sgqlc.types.Field(
        sgqlc.types.non_null(OrganizationConnection),
        graphql_name="organizations",
        args=sgqlc.types.ArgDict(
            (
                (
                    "organization_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="organizationIds",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    site = sgqlc.types.Field(
        "Site",
        graphql_name="site",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    sites = sgqlc.types.Field(
        sgqlc.types.non_null("SiteConnection"),
        graphql_name="sites",
        args=sgqlc.types.ArgDict(
            (
                (
                    "site_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="siteIds",
                        default=None,
                    ),
                ),
                (
                    "organization_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="organizationIds",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    room = sgqlc.types.Field(
        "Room",
        graphql_name="room",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    rooms = sgqlc.types.Field(
        sgqlc.types.non_null("RoomConnection"),
        graphql_name="rooms",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=None,
                    ),
                ),
                ("site_id", sgqlc.types.Arg(String, graphql_name="siteId", default=None)),
                ("org_id", sgqlc.types.Arg(String, graphql_name="orgId", default=None)),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    room_closures = sgqlc.types.Field(
        sgqlc.types.non_null("RoomClosureConnection"),
        graphql_name="roomClosures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomClosureQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    site_closures = sgqlc.types.Field(
        sgqlc.types.non_null("SiteClosureConnection"),
        graphql_name="siteClosures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(SiteClosureQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    room_tags = sgqlc.types.Field(
        sgqlc.types.non_null("RoomTagConnection"),
        graphql_name="roomTags",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=None,
                    ),
                ),
                ("org_id", sgqlc.types.Arg(String, graphql_name="orgId", default=None)),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    camera = sgqlc.types.Field(
        Camera,
        graphql_name="camera",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    cameras = sgqlc.types.Field(
        sgqlc.types.non_null(CameraConnection),
        graphql_name="cameras",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=None,
                    ),
                ),
                (
                    "families",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="families",
                        default=None,
                    ),
                ),
                (
                    "room_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="roomIds",
                        default=None,
                    ),
                ),
                (
                    "site_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="siteIds",
                        default=None,
                    ),
                ),
                (
                    "organization_id",
                    sgqlc.types.Arg(String, graphql_name="organizationId", default=None),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    cluster = sgqlc.types.Field(
        Cluster,
        graphql_name="cluster",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    clusters = sgqlc.types.Field(
        ClusterConnection,
        graphql_name="clusters",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    event = sgqlc.types.Field(
        Event,
        graphql_name="event",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    event_search = sgqlc.types.Field(
        sgqlc.types.non_null(EventConnection),
        graphql_name="eventSearch",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventSearchInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    event_history_search = sgqlc.types.Field(
        sgqlc.types.non_null(EventConnection),
        graphql_name="eventHistorySearch",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(EventHistorySearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    event_label_options = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(EventLabelOption))),
        graphql_name="eventLabelOptions",
    )
    event_type = sgqlc.types.Field(
        EventType,
        graphql_name="eventType",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    event_types = sgqlc.types.Field(
        EventTypeConnection,
        graphql_name="eventTypes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "types",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="types",
                        default=(),
                    ),
                ),
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=(),
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    highlight = sgqlc.types.Field(
        Highlight,
        graphql_name="highlight",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    highlights = sgqlc.types.Field(
        sgqlc.types.non_null(HighlightConnection),
        graphql_name="highlights",
        args=sgqlc.types.ArgDict(
            (
                ("query", sgqlc.types.Arg(HighlightInput, graphql_name="query", default=None)),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    highlight_search = sgqlc.types.Field(
        sgqlc.types.non_null(HighlightConnection),
        graphql_name="highlightSearch",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(HighlightSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    highlight_feedback_search = sgqlc.types.Field(
        sgqlc.types.non_null(HighlightFeedbackConnection),
        graphql_name="highlightFeedbackSearch",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(HighlightFeedbackSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    annotation_task = sgqlc.types.Field(
        AnnotationTask,
        graphql_name="annotationTask",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    annotation_tasks = sgqlc.types.Field(
        sgqlc.types.non_null(AnnotationTaskConnection),
        graphql_name="annotationTasks",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnnotationTaskQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    annotation_task_type = sgqlc.types.Field(
        AnnotationTaskType,
        graphql_name="annotationTaskType",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    annotation_task_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(AnnotationTaskType)),
        graphql_name="annotationTaskTypes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=(),
                    ),
                ),
            )
        ),
    )
    phases = sgqlc.types.Field(
        sgqlc.types.non_null(PhaseConnection),
        graphql_name="phases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(PhaseQueryInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    apella_cases = sgqlc.types.Field(
        sgqlc.types.non_null(ApellaCaseConnection),
        graphql_name="apellaCases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ApellaCaseQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    phase_type = sgqlc.types.Field(
        PhaseTypeRecord,
        graphql_name="phaseType",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    phase_types = sgqlc.types.Field(
        sgqlc.types.non_null(PhaseTypeConnection),
        graphql_name="phaseTypes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=(),
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    object_metrics = sgqlc.types.Field(ObjectMetrics, graphql_name="objectMetrics")
    scheduled_cases = sgqlc.types.Field(
        sgqlc.types.non_null("ScheduledCaseConnection"),
        graphql_name="scheduledCases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ScheduledCaseQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    cases = sgqlc.types.Field(
        sgqlc.types.non_null("ScheduledCaseConnection"),
        graphql_name="cases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ScheduledCaseQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    cases_history = sgqlc.types.Field(
        sgqlc.types.non_null("ScheduledCaseConnection"),
        graphql_name="casesHistory",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseHistoryQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_ehr_messages = sgqlc.types.Field(
        sgqlc.types.non_null(CaseEhrMessageConnection),
        graphql_name="caseEhrMessages",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseEhrMessageQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_classification_types = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseClassificationType)),
        graphql_name="caseClassificationTypes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="ids",
                        default=None,
                    ),
                ),
            )
        ),
    )
    live_camera_images = sgqlc.types.Field(
        sgqlc.types.non_null(CameraLatestImageConnection),
        graphql_name="liveCameraImages",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(LiveCameraImagesInput, graphql_name="query", default=None),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    service_lines = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("ServiceLine")),
        graphql_name="serviceLines",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ServiceLineQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    procedures = sgqlc.types.Field(
        sgqlc.types.non_null(ProcedureConnection),
        graphql_name="procedures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ProcedureQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    anesthesias = sgqlc.types.Field(
        sgqlc.types.non_null(AnesthesiaConnection),
        graphql_name="anesthesias",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AnesthesiaQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    measurement_period = sgqlc.types.Field(
        MeasurementPeriod,
        graphql_name="measurementPeriod",
        args=sgqlc.types.ArgDict(
            (
                (
                    "id",
                    sgqlc.types.Arg(sgqlc.types.non_null(String), graphql_name="id", default=None),
                ),
            )
        ),
    )
    measurement_periods = sgqlc.types.Field(
        sgqlc.types.non_null(MeasurementPeriodConnection),
        graphql_name="measurementPeriods",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(MeasurementPeriodQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    staff = sgqlc.types.Field(
        sgqlc.types.non_null("StaffConnection"),
        graphql_name="staff",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(StaffQueryInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    observation = sgqlc.types.Field(
        sgqlc.types.non_null(ObservationConnection),
        graphql_name="observation",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ObservationSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    observation_type_names = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(ObservationTypeName)),
        graphql_name="observationTypeNames",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ObservationTypeNamesInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    observation_type_names_for_custom_phases = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(ObservationTypeName)),
        graphql_name="observationTypeNamesForCustomPhases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ObservationTypeNamesInputForCustomPhases),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    contact_information = sgqlc.types.Field(
        sgqlc.types.non_null(ContactInformationConnection),
        graphql_name="contactInformation",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ContactInformationSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    staff_event_notification_contact_information = sgqlc.types.Field(
        sgqlc.types.non_null("StaffEventNotificationContactInformationConnection"),
        graphql_name="staffEventNotificationContactInformation",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(StaffEventNotificationContactInformationSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    staffing_needs_roles = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("StaffRole"))),
        graphql_name="staffingNeedsRoles",
    )
    block = sgqlc.types.Field(
        Block,
        graphql_name="block",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    blocks = sgqlc.types.Field(
        sgqlc.types.non_null(BlockConnection),
        graphql_name="blocks",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockQueryInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    block_time = sgqlc.types.Field(
        BlockTime,
        graphql_name="blockTime",
        args=sgqlc.types.ArgDict(
            (("id", sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="id", default=None)),)
        ),
    )
    block_times_bulk = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(BlockTime))),
        graphql_name="blockTimesBulk",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimesBulkQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_times = sgqlc.types.Field(
        sgqlc.types.non_null(BlockTimeConnection),
        graphql_name="blockTimes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimeQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    block_times_available_intervals = sgqlc.types.Field(
        sgqlc.types.non_null(BlockTimeAvailableIntervalConnection),
        graphql_name="blockTimesAvailableIntervals",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimeAvailableIntervalInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    board_configs = sgqlc.types.Field(
        sgqlc.types.non_null(BoardConfigConnection),
        graphql_name="boardConfigs",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BoardConfigQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_duration_surgeons_and_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(CaseDurationSurgeonProcedureMappingConnection),
        graphql_name="caseDurationSurgeonsAndProcedures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_duration_turnover_prediction = sgqlc.types.Field(
        CaseDurationTurnoverPrediction,
        graphql_name="caseDurationTurnoverPrediction",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseDurationPredictionQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_duration_predictions = sgqlc.types.Field(
        CaseDurationPredictions,
        graphql_name="caseDurationPredictions",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseDurationPredictionQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_duration_surgeons = sgqlc.types.Field(
        sgqlc.types.non_null(CaseDurationSurgeonOptionConnection),
        graphql_name="caseDurationSurgeons",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseDurationSurgeonsQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_duration_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(CaseDurationProcedureOptionConnection),
        graphql_name="caseDurationProcedures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseDurationProceduresQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    available_time_slots = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(AvailableTimeSlot)),
        graphql_name="availableTimeSlots",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(AvailableTimeSlotQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    user_filter_views = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("UserFilterView")),
        graphql_name="userFilterViews",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(UserFilterViewQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    case_forecasts = sgqlc.types.Field(
        sgqlc.types.non_null(CaseForecastConnection),
        graphql_name="caseForecasts",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseForecastQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    dashboard_events = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(EventDashboardVisibility))),
        graphql_name="dashboardEvents",
    )
    turnover_labels = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("TurnoverLabel"))),
        graphql_name="turnoverLabels",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(GQLTurnoverLabelQueryInput, graphql_name="query", default=None),
                ),
            )
        ),
    )
    cases_to_blocks = sgqlc.types.Field(
        sgqlc.types.non_null(CaseToBlockConnection),
        graphql_name="casesToBlocks",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseToBlockInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_to_block_overrides = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(CaseToBlockOverride)),
        graphql_name="caseToBlockOverrides",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseToBlockOverrideInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    block_utilizations = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(BlockUtilization)),
        graphql_name="blockUtilizations",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockUtilizationInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
            )
        ),
    )
    custom_phase_configs = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CustomPhaseConfig))),
        graphql_name="customPhaseConfigs",
    )
    terminal_clean_score = sgqlc.types.Field(
        "TerminalCleanScore",
        graphql_name="terminalCleanScore",
        args=sgqlc.types.ArgDict(
            (
                (
                    "room_id",
                    sgqlc.types.Arg(sgqlc.types.non_null(ID), graphql_name="roomId", default=None),
                ),
                (
                    "date",
                    sgqlc.types.Arg(sgqlc.types.non_null(Date), graphql_name="date", default=None),
                ),
            )
        ),
    )


class Room(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "organization_id",
        "organization",
        "site_id",
        "site",
        "cameras",
        "default_camera",
        "status",
        "sort_key",
        "has_playlist_available",
        "events",
        "turnovers",
        "apella_cases",
        "room_events",
        "block_times",
        "privacy_enabled",
        "is_forecasting_enabled",
        "tags",
        "closures",
        "prime_time_config",
        "first_case_config",
        "labels",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    cameras = sgqlc.types.Field(
        sgqlc.types.non_null(CameraConnection),
        graphql_name="cameras",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    default_camera = sgqlc.types.Field(Camera, graphql_name="defaultCamera")
    status = sgqlc.types.Field(sgqlc.types.non_null("RoomStatus"), graphql_name="status")
    sort_key = sgqlc.types.Field(String, graphql_name="sortKey")
    has_playlist_available = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean),
        graphql_name="hasPlaylistAvailable",
        args=sgqlc.types.ArgDict(
            (
                (
                    "min_time",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(DateTime), graphql_name="minTime", default=None
                    ),
                ),
                (
                    "max_time",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(DateTime), graphql_name="maxTime", default=None
                    ),
                ),
            )
        ),
    )
    events = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(Event))),
        graphql_name="events",
        args=sgqlc.types.ArgDict(
            (
                (
                    "min_time",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(DateTime), graphql_name="minTime", default=None
                    ),
                ),
                (
                    "max_time",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(DateTime), graphql_name="maxTime", default=None
                    ),
                ),
                ("source_type", sgqlc.types.Arg(String, graphql_name="sourceType", default=None)),
                (
                    "model_version",
                    sgqlc.types.Arg(String, graphql_name="modelVersion", default=None),
                ),
                ("event_type", sgqlc.types.Arg(String, graphql_name="eventType", default=None)),
                (
                    "event_names",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="eventNames",
                        default=None,
                    ),
                ),
            )
        ),
    )
    turnovers = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("Turnover"))),
        graphql_name="turnovers",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(TurnoverQueryInput), graphql_name="query", default=None
                    ),
                ),
            )
        ),
    )
    apella_cases = sgqlc.types.Field(
        sgqlc.types.non_null(ApellaCaseConnection),
        graphql_name="apellaCases",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(ApellaCaseBaseQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    room_events = sgqlc.types.Field(
        sgqlc.types.non_null(EventConnection),
        graphql_name="roomEvents",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(RoomEventSearchInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    block_times = sgqlc.types.Field(
        sgqlc.types.non_null(BlockTimeConnection),
        graphql_name="blockTimes",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(BlockTimeQueryInput),
                        graphql_name="query",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    privacy_enabled = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="privacyEnabled"
    )
    is_forecasting_enabled = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="isForecastingEnabled"
    )
    tags = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("RoomTag"))),
        graphql_name="tags",
    )
    closures = sgqlc.types.Field(
        sgqlc.types.non_null("RoomClosureConnection"),
        graphql_name="closures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    prime_time_config = sgqlc.types.Field(
        sgqlc.types.non_null("RoomPrimeTimeConfig"), graphql_name="primeTimeConfig"
    )
    first_case_config = sgqlc.types.Field(
        sgqlc.types.non_null("RoomFirstCaseConfig"), graphql_name="firstCaseConfig"
    )
    labels = sgqlc.types.Field(JSON, graphql_name="labels")


class RoomClosure(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "room_id", "room", "start_time", "end_time", "reason")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    reason = sgqlc.types.Field(String, graphql_name="reason")


class RoomClosureConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("RoomClosureEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class RoomClosureCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room", "created_room_closure")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    created_room_closure = sgqlc.types.Field(RoomClosure, graphql_name="createdRoomClosure")


class RoomClosureDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")


class RoomClosureEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(RoomClosure), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class RoomConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("RoomEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class RoomCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_room = sgqlc.types.Field(Room, graphql_name="createdRoom")


class RoomEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class RoomFirstCaseConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "room_id",
        "room",
        "source",
        "sunday_start_time",
        "sunday_end_time",
        "monday_start_time",
        "monday_end_time",
        "tuesday_start_time",
        "tuesday_end_time",
        "wednesday_start_time",
        "wednesday_end_time",
        "thursday_start_time",
        "thursday_end_time",
        "friday_start_time",
        "friday_end_time",
        "saturday_start_time",
        "saturday_end_time",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    room_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    source = sgqlc.types.Field(sgqlc.types.non_null(FirstCaseConfigSource), graphql_name="source")
    sunday_start_time = sgqlc.types.Field(Time, graphql_name="sundayStartTime")
    sunday_end_time = sgqlc.types.Field(Time, graphql_name="sundayEndTime")
    monday_start_time = sgqlc.types.Field(Time, graphql_name="mondayStartTime")
    monday_end_time = sgqlc.types.Field(Time, graphql_name="mondayEndTime")
    tuesday_start_time = sgqlc.types.Field(Time, graphql_name="tuesdayStartTime")
    tuesday_end_time = sgqlc.types.Field(Time, graphql_name="tuesdayEndTime")
    wednesday_start_time = sgqlc.types.Field(Time, graphql_name="wednesdayStartTime")
    wednesday_end_time = sgqlc.types.Field(Time, graphql_name="wednesdayEndTime")
    thursday_start_time = sgqlc.types.Field(Time, graphql_name="thursdayStartTime")
    thursday_end_time = sgqlc.types.Field(Time, graphql_name="thursdayEndTime")
    friday_start_time = sgqlc.types.Field(Time, graphql_name="fridayStartTime")
    friday_end_time = sgqlc.types.Field(Time, graphql_name="fridayEndTime")
    saturday_start_time = sgqlc.types.Field(Time, graphql_name="saturdayStartTime")
    saturday_end_time = sgqlc.types.Field(Time, graphql_name="saturdayEndTime")
    sunday = sgqlc.types.Field("TimeRange", graphql_name="sunday")
    monday = sgqlc.types.Field("TimeRange", graphql_name="monday")
    tuesday = sgqlc.types.Field("TimeRange", graphql_name="tuesday")
    wednesday = sgqlc.types.Field("TimeRange", graphql_name="wednesday")
    thursday = sgqlc.types.Field("TimeRange", graphql_name="thursday")
    friday = sgqlc.types.Field("TimeRange", graphql_name="friday")
    saturday = sgqlc.types.Field("TimeRange", graphql_name="saturday")


class RoomFirstCaseConfigDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")


class RoomFirstCaseConfigUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room", "first_case_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    first_case_config = sgqlc.types.Field(RoomFirstCaseConfig, graphql_name="firstCaseConfig")


class RoomPrimeTimeConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "room_id",
        "room",
        "source",
        "sunday_start_time",
        "sunday_end_time",
        "monday_start_time",
        "monday_end_time",
        "tuesday_start_time",
        "tuesday_end_time",
        "wednesday_start_time",
        "wednesday_end_time",
        "thursday_start_time",
        "thursday_end_time",
        "friday_start_time",
        "friday_end_time",
        "saturday_start_time",
        "saturday_end_time",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    source = sgqlc.types.Field(sgqlc.types.non_null(PrimeTimeConfigSource), graphql_name="source")
    sunday_start_time = sgqlc.types.Field(Time, graphql_name="sundayStartTime")
    sunday_end_time = sgqlc.types.Field(Time, graphql_name="sundayEndTime")
    monday_start_time = sgqlc.types.Field(Time, graphql_name="mondayStartTime")
    monday_end_time = sgqlc.types.Field(Time, graphql_name="mondayEndTime")
    tuesday_start_time = sgqlc.types.Field(Time, graphql_name="tuesdayStartTime")
    tuesday_end_time = sgqlc.types.Field(Time, graphql_name="tuesdayEndTime")
    wednesday_start_time = sgqlc.types.Field(Time, graphql_name="wednesdayStartTime")
    wednesday_end_time = sgqlc.types.Field(Time, graphql_name="wednesdayEndTime")
    thursday_start_time = sgqlc.types.Field(Time, graphql_name="thursdayStartTime")
    thursday_end_time = sgqlc.types.Field(Time, graphql_name="thursdayEndTime")
    friday_start_time = sgqlc.types.Field(Time, graphql_name="fridayStartTime")
    friday_end_time = sgqlc.types.Field(Time, graphql_name="fridayEndTime")
    saturday_start_time = sgqlc.types.Field(Time, graphql_name="saturdayStartTime")
    saturday_end_time = sgqlc.types.Field(Time, graphql_name="saturdayEndTime")
    sunday = sgqlc.types.Field("TimeRange", graphql_name="sunday")
    monday = sgqlc.types.Field("TimeRange", graphql_name="monday")
    tuesday = sgqlc.types.Field("TimeRange", graphql_name="tuesday")
    wednesday = sgqlc.types.Field("TimeRange", graphql_name="wednesday")
    thursday = sgqlc.types.Field("TimeRange", graphql_name="thursday")
    friday = sgqlc.types.Field("TimeRange", graphql_name="friday")
    saturday = sgqlc.types.Field("TimeRange", graphql_name="saturday")


class RoomPrimeTimeConfigDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")


class RoomPrimeTimeConfigUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room", "room_prime_time_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    room_prime_time_config = sgqlc.types.Field(
        RoomPrimeTimeConfig, graphql_name="roomPrimeTimeConfig"
    )


class RoomSetTags(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    room = sgqlc.types.Field(Room, graphql_name="room")


class RoomStatus(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "name",
        "since",
        "calculated_at",
        "in_progress_apella_case",
        "in_progress_turnover",
        "next_case",
    )
    name = sgqlc.types.Field(sgqlc.types.non_null(RoomStatusName), graphql_name="name")
    since = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="since")
    calculated_at = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="calculatedAt")
    in_progress_apella_case = sgqlc.types.Field(ApellaCase, graphql_name="inProgressApellaCase")
    in_progress_turnover = sgqlc.types.Field("Turnover", graphql_name="inProgressTurnover")
    next_case = sgqlc.types.Field(ApellaCase, graphql_name="nextCase")


class RoomTag(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "org_id", "organization_id", "organization", "name", "color")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    color = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="color")


class RoomTagConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("RoomTagEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class RoomTagCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_room_tag")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_room_tag = sgqlc.types.Field(RoomTag, graphql_name="createdRoomTag")


class RoomTagEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(RoomTag), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class RoomTagRename(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_room_tag")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_room_tag = sgqlc.types.Field(RoomTag, graphql_name="updatedRoomTag")


class RoomTagUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_room_tag")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_room_tag = sgqlc.types.Field(RoomTag, graphql_name="updatedRoomTag")


class RoomUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_room = sgqlc.types.Field(Room, graphql_name="updatedRoom")


class RoomUpdateConfiguration(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_room")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_room = sgqlc.types.Field(Room, graphql_name="updatedRoom")


class RoomsCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_rooms")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_rooms = sgqlc.types.Field(sgqlc.types.list_of(Room), graphql_name="createdRooms")


class ScheduledCase(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "scheduled_start_time",
        "scheduled_end_time",
        "updated_time",
        "created_time",
        "site",
        "room",
        "case_classification_type",
        "status",
        "case_staff",
        "case_staff_plan",
        "note_plan",
        "staff",
        "is_in_flip_room",
        "preceding_case",
        "is_first_case",
        "case_procedures",
        "procedures",
        "is_add_on",
        "patient",
        "patient_class",
        "version",
        "observations",
        "external_case_id",
        "primary_case_procedures",
        "case_flags",
        "service_line",
        "case_matching_status",
        "matching_status_reason",
        "event_notifications",
        "case_labels",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    scheduled_start_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="scheduledStartTime"
    )
    scheduled_end_time = sgqlc.types.Field(
        sgqlc.types.non_null(DateTime), graphql_name="scheduledEndTime"
    )
    updated_time = sgqlc.types.Field(DateTime, graphql_name="updatedTime")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    site = sgqlc.types.Field(sgqlc.types.non_null("Site"), graphql_name="site")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    case_classification_type = sgqlc.types.Field(
        CaseClassificationType, graphql_name="caseClassificationType"
    )
    status = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="status")
    case_staff = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseStaff))),
        graphql_name="caseStaff",
        args=sgqlc.types.ArgDict(
            (
                (
                    "only_primary_surgeons",
                    sgqlc.types.Arg(Boolean, graphql_name="onlyPrimarySurgeons", default=None),
                ),
            )
        ),
    )
    case_staff_plan = sgqlc.types.Field(
        sgqlc.types.non_null(CaseStaffPlanConnection),
        graphql_name="caseStaffPlan",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(
                        sgqlc.types.non_null(CaseStaffPlanInput), graphql_name="query", default=None
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    note_plan = sgqlc.types.Field(CaseNotePlan, graphql_name="notePlan")
    staff = sgqlc.types.Field(
        sgqlc.types.non_null("StaffConnection"),
        graphql_name="staff",
        args=sgqlc.types.ArgDict(
            (
                (
                    "only_primary_surgeons",
                    sgqlc.types.Arg(Boolean, graphql_name="onlyPrimarySurgeons", default=None),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    is_in_flip_room = sgqlc.types.Field(Boolean, graphql_name="isInFlipRoom")
    preceding_case = sgqlc.types.Field("ScheduledCase", graphql_name="precedingCase")
    is_first_case = sgqlc.types.Field(Boolean, graphql_name="isFirstCase")
    case_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseProcedure))),
        graphql_name="caseProcedures",
        args=sgqlc.types.ArgDict(
            (("hierarchy", sgqlc.types.Arg(Int, graphql_name="hierarchy", default=None)),)
        ),
    )
    procedures = sgqlc.types.Field(
        sgqlc.types.non_null(ProcedureConnection),
        graphql_name="procedures",
        args=sgqlc.types.ArgDict(
            (
                ("hierarchy", sgqlc.types.Arg(Int, graphql_name="hierarchy", default=None)),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    is_add_on = sgqlc.types.Field(Boolean, graphql_name="isAddOn")
    patient = sgqlc.types.Field(Patient, graphql_name="patient")
    patient_class = sgqlc.types.Field(PatientClass, graphql_name="patientClass")
    version = sgqlc.types.Field(Int, graphql_name="version")
    observations = sgqlc.types.Field(
        sgqlc.types.non_null(ObservationConnection),
        graphql_name="observations",
        args=sgqlc.types.ArgDict(
            (
                (
                    "types",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="types",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    external_case_id = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="externalCaseId"
    )
    primary_case_procedures = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseProcedure))),
        graphql_name="primaryCaseProcedures",
    )
    case_flags = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseFlag))),
        graphql_name="caseFlags",
        args=sgqlc.types.ArgDict(
            (
                (
                    "flag_types",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(String), graphql_name="flagTypes", default=None
                    ),
                ),
                (
                    "include_archived",
                    sgqlc.types.Arg(Boolean, graphql_name="includeArchived", default=None),
                ),
            )
        ),
    )
    service_line = sgqlc.types.Field("ServiceLine", graphql_name="serviceLine")
    case_matching_status = sgqlc.types.Field(
        sgqlc.types.non_null(CaseMatchingStatus), graphql_name="caseMatchingStatus"
    )
    matching_status_reason = sgqlc.types.Field(
        MatchingStatusReason, graphql_name="matchingStatusReason"
    )
    event_notifications = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(EventNotification)),
        graphql_name="eventNotifications",
    )
    case_labels = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseLabel))),
        graphql_name="caseLabels",
    )


class ScheduledCaseConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("ScheduledCaseEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class ScheduledCaseEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(ScheduledCase), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class ServiceLine(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "external_service_line_id", "org_id", "org")
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    name = sgqlc.types.Field(String, graphql_name="name")
    external_service_line_id = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="externalServiceLineId"
    )
    org_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="orgId")
    org = sgqlc.types.Field(sgqlc.types.non_null(Organization), graphql_name="org")


class Site(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "timezone",
        "organization_id",
        "organization",
        "rooms",
        "turnover_goals",
        "prime_time_config",
        "first_case_config",
        "capacity_constraints",
        "staffing_needs_ratios",
        "closures",
        "case_label_form",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    timezone = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="timezone")
    organization_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="organizationId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    rooms = sgqlc.types.Field(
        sgqlc.types.non_null(RoomConnection),
        graphql_name="rooms",
        args=sgqlc.types.ArgDict(
            (
                (
                    "room_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="roomIds",
                        default=None,
                    ),
                ),
                (
                    "status_filter",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(RoomStatusName)),
                        graphql_name="statusFilter",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    turnover_goals = sgqlc.types.Field(
        sgqlc.types.non_null("TurnoverGoals"), graphql_name="turnoverGoals"
    )
    prime_time_config = sgqlc.types.Field(
        sgqlc.types.non_null("SitePrimeTimeConfig"), graphql_name="primeTimeConfig"
    )
    first_case_config = sgqlc.types.Field(
        sgqlc.types.non_null("SiteFirstCaseConfig"), graphql_name="firstCaseConfig"
    )
    capacity_constraints = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("SiteCapacityConstraint"))),
        graphql_name="capacityConstraints",
    )
    staffing_needs_ratios = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("StaffingNeedsRatio"))),
        graphql_name="staffingNeedsRatios",
    )
    closures = sgqlc.types.Field(
        "SiteClosureConnection",
        graphql_name="closures",
        args=sgqlc.types.ArgDict(
            (
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    case_label_form = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseLabelCategory))),
        graphql_name="caseLabelForm",
    )


class SiteCapacityConstraint(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "site_id", "site", "day_of_week", "count", "start_time")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    day_of_week = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="dayOfWeek")
    count = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="count")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")


class SiteClosure(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "site_id", "site", "closure_date", "reason")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    closure_date = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="closureDate")
    reason = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="reason")


class SiteClosureConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("SiteClosureEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class SiteClosureCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "site", "created_site_closure")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    created_site_closure = sgqlc.types.Field(SiteClosure, graphql_name="createdSiteClosure")


class SiteClosureDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "site")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")


class SiteClosureEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(SiteClosure), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class SiteConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("SiteEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class SiteCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_site")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_site = sgqlc.types.Field(Site, graphql_name="createdSite")


class SiteEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class SiteFirstCaseConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "site_id",
        "site",
        "source",
        "sunday_start_time",
        "sunday_end_time",
        "monday_start_time",
        "monday_end_time",
        "tuesday_start_time",
        "tuesday_end_time",
        "wednesday_start_time",
        "wednesday_end_time",
        "thursday_start_time",
        "thursday_end_time",
        "friday_start_time",
        "friday_end_time",
        "saturday_start_time",
        "saturday_end_time",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    site_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    source = sgqlc.types.Field(
        sgqlc.types.non_null(SiteFirstCaseConfigSource), graphql_name="source"
    )
    sunday_start_time = sgqlc.types.Field(Time, graphql_name="sundayStartTime")
    sunday_end_time = sgqlc.types.Field(Time, graphql_name="sundayEndTime")
    monday_start_time = sgqlc.types.Field(Time, graphql_name="mondayStartTime")
    monday_end_time = sgqlc.types.Field(Time, graphql_name="mondayEndTime")
    tuesday_start_time = sgqlc.types.Field(Time, graphql_name="tuesdayStartTime")
    tuesday_end_time = sgqlc.types.Field(Time, graphql_name="tuesdayEndTime")
    wednesday_start_time = sgqlc.types.Field(Time, graphql_name="wednesdayStartTime")
    wednesday_end_time = sgqlc.types.Field(Time, graphql_name="wednesdayEndTime")
    thursday_start_time = sgqlc.types.Field(Time, graphql_name="thursdayStartTime")
    thursday_end_time = sgqlc.types.Field(Time, graphql_name="thursdayEndTime")
    friday_start_time = sgqlc.types.Field(Time, graphql_name="fridayStartTime")
    friday_end_time = sgqlc.types.Field(Time, graphql_name="fridayEndTime")
    saturday_start_time = sgqlc.types.Field(Time, graphql_name="saturdayStartTime")
    saturday_end_time = sgqlc.types.Field(Time, graphql_name="saturdayEndTime")
    sunday = sgqlc.types.Field("TimeRange", graphql_name="sunday")
    monday = sgqlc.types.Field("TimeRange", graphql_name="monday")
    tuesday = sgqlc.types.Field("TimeRange", graphql_name="tuesday")
    wednesday = sgqlc.types.Field("TimeRange", graphql_name="wednesday")
    thursday = sgqlc.types.Field("TimeRange", graphql_name="thursday")
    friday = sgqlc.types.Field("TimeRange", graphql_name="friday")
    saturday = sgqlc.types.Field("TimeRange", graphql_name="saturday")


class SiteFirstCaseConfigUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "site", "first_case_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    first_case_config = sgqlc.types.Field(SiteFirstCaseConfig, graphql_name="firstCaseConfig")


class SiteLaunchesUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class SitePrimeTimeConfig(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "site_id",
        "site",
        "source",
        "sunday_start_time",
        "sunday_end_time",
        "monday_start_time",
        "monday_end_time",
        "tuesday_start_time",
        "tuesday_end_time",
        "wednesday_start_time",
        "wednesday_end_time",
        "thursday_start_time",
        "thursday_end_time",
        "friday_start_time",
        "friday_end_time",
        "saturday_start_time",
        "saturday_end_time",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    source = sgqlc.types.Field(
        sgqlc.types.non_null(SitePrimeTimeConfigSource), graphql_name="source"
    )
    sunday_start_time = sgqlc.types.Field(Time, graphql_name="sundayStartTime")
    sunday_end_time = sgqlc.types.Field(Time, graphql_name="sundayEndTime")
    monday_start_time = sgqlc.types.Field(Time, graphql_name="mondayStartTime")
    monday_end_time = sgqlc.types.Field(Time, graphql_name="mondayEndTime")
    tuesday_start_time = sgqlc.types.Field(Time, graphql_name="tuesdayStartTime")
    tuesday_end_time = sgqlc.types.Field(Time, graphql_name="tuesdayEndTime")
    wednesday_start_time = sgqlc.types.Field(Time, graphql_name="wednesdayStartTime")
    wednesday_end_time = sgqlc.types.Field(Time, graphql_name="wednesdayEndTime")
    thursday_start_time = sgqlc.types.Field(Time, graphql_name="thursdayStartTime")
    thursday_end_time = sgqlc.types.Field(Time, graphql_name="thursdayEndTime")
    friday_start_time = sgqlc.types.Field(Time, graphql_name="fridayStartTime")
    friday_end_time = sgqlc.types.Field(Time, graphql_name="fridayEndTime")
    saturday_start_time = sgqlc.types.Field(Time, graphql_name="saturdayStartTime")
    saturday_end_time = sgqlc.types.Field(Time, graphql_name="saturdayEndTime")
    sunday = sgqlc.types.Field("TimeRange", graphql_name="sunday")
    monday = sgqlc.types.Field("TimeRange", graphql_name="monday")
    tuesday = sgqlc.types.Field("TimeRange", graphql_name="tuesday")
    wednesday = sgqlc.types.Field("TimeRange", graphql_name="wednesday")
    thursday = sgqlc.types.Field("TimeRange", graphql_name="thursday")
    friday = sgqlc.types.Field("TimeRange", graphql_name="friday")
    saturday = sgqlc.types.Field("TimeRange", graphql_name="saturday")


class SitePrimeTimeConfigUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "site", "site_prime_time_config")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    site = sgqlc.types.Field(sgqlc.types.non_null(Site), graphql_name="site")
    site_prime_time_config = sgqlc.types.Field(
        SitePrimeTimeConfig, graphql_name="sitePrimeTimeConfig"
    )


class SiteUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_site")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_site = sgqlc.types.Field(Site, graphql_name="updatedSite")


class Staff(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "first_name",
        "last_name",
        "name",
        "external_staff_id",
        "codes",
        "staff_event_notification_contact_information",
        "organization",
        "most_frequent_site",
        "block_ids",
        "blocks",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    first_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="firstName")
    last_name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="lastName")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    external_staff_id = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="externalStaffId"
    )
    codes = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("StaffCode")), graphql_name="codes"
    )
    staff_event_notification_contact_information = sgqlc.types.Field(
        sgqlc.types.non_null("StaffEventNotificationContactInformationConnection"),
        graphql_name="staffEventNotificationContactInformation",
        args=sgqlc.types.ArgDict(
            (
                (
                    "contact_information_event_type_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="contactInformationEventTypeIds",
                        default=None,
                    ),
                ),
                ("initialized", sgqlc.types.Arg(Boolean, graphql_name="initialized", default=None)),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )
    most_frequent_site = sgqlc.types.Field(Site, graphql_name="mostFrequentSite")
    block_ids = sgqlc.types.Field(sgqlc.types.list_of(String), graphql_name="blockIds")
    blocks = sgqlc.types.Field(sgqlc.types.list_of(Block), graphql_name="blocks")


class StaffCode(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("coding_system", "code")
    coding_system = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="codingSystem")
    code = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="code")


class StaffCodesUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_staff_codes")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_staff_codes = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(StaffCode))),
        graphql_name="createdStaffCodes",
    )


class StaffConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("StaffEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class StaffEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(Staff), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class StaffEventNotificationContactInformation(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "staff_id",
        "staff",
        "event_type_id",
        "event_type",
        "observation_type",
        "contact_information",
        "contact_information_value",
        "contact_information_type",
        "contact_information_initialized",
        "event_notifications",
        "staff_event_notifications",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    staff_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="staffId")
    staff = sgqlc.types.Field(sgqlc.types.non_null(Staff), graphql_name="staff")
    event_type_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="eventTypeId")
    event_type = sgqlc.types.Field(sgqlc.types.non_null(EventType), graphql_name="eventType")
    observation_type = sgqlc.types.Field(ObservationType, graphql_name="observationType")
    contact_information = sgqlc.types.Field(
        sgqlc.types.non_null(ContactInformation), graphql_name="contactInformation"
    )
    contact_information_value = sgqlc.types.Field(
        sgqlc.types.non_null(String), graphql_name="contactInformationValue"
    )
    contact_information_type = sgqlc.types.Field(
        sgqlc.types.non_null(ContactInformationType), graphql_name="contactInformationType"
    )
    contact_information_initialized = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="contactInformationInitialized"
    )
    event_notifications = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(EventNotification))),
        graphql_name="eventNotifications",
        args=sgqlc.types.ArgDict(
            (
                (
                    "event_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="eventIds",
                        default=None,
                    ),
                ),
            )
        ),
    )
    staff_event_notifications = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(EventNotification))),
        graphql_name="staffEventNotifications",
        args=sgqlc.types.ArgDict(
            (
                (
                    "query",
                    sgqlc.types.Arg(EventNotficationQueryInput, graphql_name="query", default=None),
                ),
            )
        ),
    )


class StaffEventNotificationContactInformationConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(
            sgqlc.types.list_of(
                sgqlc.types.non_null("StaffEventNotificationContactInformationEdge")
            )
        ),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class StaffEventNotificationContactInformationEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(
        sgqlc.types.non_null(StaffEventNotificationContactInformation), graphql_name="node"
    )
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class StaffRole(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name")
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")


class StaffUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_staff")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_staff = sgqlc.types.Field(
        sgqlc.types.non_null(StaffConnection),
        graphql_name="createdStaff",
        args=sgqlc.types.ArgDict(
            (
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )


class StaffingNeedsRatio(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "site_id", "ratio", "staff_role", "set_by_user_id")
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    site_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="siteId")
    ratio = sgqlc.types.Field(Float, graphql_name="ratio")
    staff_role = sgqlc.types.Field(sgqlc.types.non_null(StaffRole), graphql_name="staffRole")
    set_by_user_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="setByUserId")


class StaffingNeedsRatioCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_staffing_needs_ratio")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    created_staffing_needs_ratio = sgqlc.types.Field(
        StaffingNeedsRatio, graphql_name="createdStaffingNeedsRatio"
    )


class SubscribersUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "contact_information")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    contact_information = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(ContactInformation))),
        graphql_name="contactInformation",
    )


class TerminalCleanScore(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "room_id", "room", "date", "score", "comments")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    room_id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="roomId")
    room = sgqlc.types.Field(sgqlc.types.non_null(Room), graphql_name="room")
    date = sgqlc.types.Field(sgqlc.types.non_null(Date), graphql_name="date")
    score = sgqlc.types.Field(CleanScoreEnum, graphql_name="score")
    comments = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="comments")


class TerminalCleanScoreUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "terminal_clean_score")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    terminal_clean_score = sgqlc.types.Field(TerminalCleanScore, graphql_name="terminalCleanScore")


class TimeRange(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("start_time", "end_time")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(Time), graphql_name="endTime")


class Turnover(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "type",
        "status",
        "start_time",
        "end_time",
        "preceding_case",
        "following_case",
        "meets_inclusion_criteria",
        "labels",
        "note",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    type = sgqlc.types.Field(sgqlc.types.non_null(TurnoverType), graphql_name="type")
    status = sgqlc.types.Field("TurnoverStatusGraphene", graphql_name="status")
    start_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="startTime")
    end_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="endTime")
    preceding_case = sgqlc.types.Field(
        sgqlc.types.non_null(ApellaCase), graphql_name="precedingCase"
    )
    following_case = sgqlc.types.Field(
        sgqlc.types.non_null(ApellaCase), graphql_name="followingCase"
    )
    meets_inclusion_criteria = sgqlc.types.Field(
        sgqlc.types.non_null(Boolean), graphql_name="meetsInclusionCriteria"
    )
    labels = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null("TurnoverLabel")), graphql_name="labels"
    )
    note = sgqlc.types.Field(String, graphql_name="note")


class TurnoverGoals(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("goal_minutes", "max_minutes")
    goal_minutes = sgqlc.types.Field(Int, graphql_name="goalMinutes")
    max_minutes = sgqlc.types.Field(sgqlc.types.non_null(Int), graphql_name="maxMinutes")


class TurnoverGoalsUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "updated_turnover_goals")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    updated_turnover_goals = sgqlc.types.Field(TurnoverGoals, graphql_name="updatedTurnoverGoals")


class TurnoverLabel(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "type")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    type = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="type")


class TurnoverLabelNoteUpsert(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "id", "note", "label_ids")
    success = sgqlc.types.Field(Boolean, graphql_name="success")
    id = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="id")
    note = sgqlc.types.Field(String, graphql_name="note")
    label_ids = sgqlc.types.Field(
        sgqlc.types.list_of(sgqlc.types.non_null(String)), graphql_name="labelIds"
    )


class TurnoverStatusGraphene(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("name", "since")
    name = sgqlc.types.Field(sgqlc.types.non_null(TurnoverStatusName), graphql_name="name")
    since = sgqlc.types.Field(DateTime, graphql_name="since")


class UpsertForecastsForCases(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success", "created_forecasts")
    success = sgqlc.types.Field(sgqlc.types.non_null(Boolean), graphql_name="success")
    created_forecasts = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null(CaseForecast))),
        graphql_name="createdForecasts",
    )


class User(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("id", "name", "email", "organizations", "ui_permissions", "permissions")
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    email = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="email")
    organizations = sgqlc.types.Field(
        sgqlc.types.non_null(OrganizationConnection),
        graphql_name="organizations",
        args=sgqlc.types.ArgDict(
            (
                (
                    "organization_ids",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(String)),
                        graphql_name="organizationIds",
                        default=None,
                    ),
                ),
                (
                    "order_by",
                    sgqlc.types.Arg(
                        sgqlc.types.list_of(sgqlc.types.non_null(OrderBy)),
                        graphql_name="orderBy",
                        default=None,
                    ),
                ),
                ("before", sgqlc.types.Arg(String, graphql_name="before", default=None)),
                ("after", sgqlc.types.Arg(String, graphql_name="after", default=None)),
                ("first", sgqlc.types.Arg(Int, graphql_name="first", default=None)),
                ("last", sgqlc.types.Arg(Int, graphql_name="last", default=None)),
            )
        ),
    )
    ui_permissions = sgqlc.types.Field("UserUiPermissions", graphql_name="uiPermissions")
    permissions = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(String)), graphql_name="permissions"
    )


class UserConnection(sgqlc.types.relay.Connection):
    __schema__ = api_server_schema
    __field_names__ = ("page_info", "edges", "page_cursors", "total_records")
    page_info = sgqlc.types.Field(sgqlc.types.non_null(PageInfo), graphql_name="pageInfo")
    edges = sgqlc.types.Field(
        sgqlc.types.non_null(sgqlc.types.list_of(sgqlc.types.non_null("UserEdge"))),
        graphql_name="edges",
    )
    page_cursors = sgqlc.types.Field(PageCursors, graphql_name="pageCursors")
    total_records = sgqlc.types.Field(Int, graphql_name="totalRecords")


class UserEdge(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("node", "cursor")
    node = sgqlc.types.Field(sgqlc.types.non_null(User), graphql_name="node")
    cursor = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="cursor")


class UserFilterView(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "id",
        "name",
        "url",
        "created_time",
        "updated_time",
        "user_id",
        "org_id",
        "organization",
    )
    id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="id")
    name = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="name")
    url = sgqlc.types.Field(sgqlc.types.non_null(String), graphql_name="url")
    created_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="createdTime")
    updated_time = sgqlc.types.Field(sgqlc.types.non_null(DateTime), graphql_name="updatedTime")
    user_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="userId")
    org_id = sgqlc.types.Field(sgqlc.types.non_null(ID), graphql_name="orgId")
    organization = sgqlc.types.Field(
        sgqlc.types.non_null(Organization), graphql_name="organization"
    )


class UserFilterViewCreate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("user_filter_view",)
    user_filter_view = sgqlc.types.Field(UserFilterView, graphql_name="userFilterView")


class UserFilterViewDelete(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("success",)
    success = sgqlc.types.Field(Boolean, graphql_name="success")


class UserFilterViewUpdate(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = ("user_filter_view",)
    user_filter_view = sgqlc.types.Field(UserFilterView, graphql_name="userFilterView")


class UserUiPermissions(sgqlc.types.Type):
    __schema__ = api_server_schema
    __field_names__ = (
        "big_board_enabled",
        "big_board_write_enabled",
        "case_duration_enabled",
        "case_staff_plan_read_enabled",
        "case_staff_plan_write_enabled",
        "contact_information_read_enabled",
        "contact_information_write_enabled",
        "dashboard_create_measurement_periods_enabled",
        "dashboard_highlights_enabled",
        "dashboard_insights_enabled",
        "dashboard_live_enabled",
        "dashboard_live_from_schedule_enabled",
        "dashboard_schedule_edit_enabled",
        "dashboard_schedule_enabled",
        "dashboard_staff_management_enabled",
        "dashboard_terminal_cleans_enabled",
        "available_time_email_enabled",
        "notification_read_enabled",
        "notification_write_enabled",
        "read_any_asset_enabled",
        "room_write_enabled",
        "room_write_configuration_enabled",
    )
    big_board_enabled = sgqlc.types.Field(Boolean, graphql_name="bigBoardEnabled")
    big_board_write_enabled = sgqlc.types.Field(Boolean, graphql_name="bigBoardWriteEnabled")
    case_duration_enabled = sgqlc.types.Field(Boolean, graphql_name="caseDurationEnabled")
    case_staff_plan_read_enabled = sgqlc.types.Field(
        Boolean, graphql_name="caseStaffPlanReadEnabled"
    )
    case_staff_plan_write_enabled = sgqlc.types.Field(
        Boolean, graphql_name="caseStaffPlanWriteEnabled"
    )
    contact_information_read_enabled = sgqlc.types.Field(
        Boolean, graphql_name="contactInformationReadEnabled"
    )
    contact_information_write_enabled = sgqlc.types.Field(
        Boolean, graphql_name="contactInformationWriteEnabled"
    )
    dashboard_create_measurement_periods_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardCreateMeasurementPeriodsEnabled"
    )
    dashboard_highlights_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardHighlightsEnabled"
    )
    dashboard_insights_enabled = sgqlc.types.Field(Boolean, graphql_name="dashboardInsightsEnabled")
    dashboard_live_enabled = sgqlc.types.Field(Boolean, graphql_name="dashboardLiveEnabled")
    dashboard_live_from_schedule_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardLiveFromScheduleEnabled"
    )
    dashboard_schedule_edit_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardScheduleEditEnabled"
    )
    dashboard_schedule_enabled = sgqlc.types.Field(Boolean, graphql_name="dashboardScheduleEnabled")
    dashboard_staff_management_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardStaffManagementEnabled"
    )
    dashboard_terminal_cleans_enabled = sgqlc.types.Field(
        Boolean, graphql_name="dashboardTerminalCleansEnabled"
    )
    available_time_email_enabled = sgqlc.types.Field(
        Boolean, graphql_name="availableTimeEmailEnabled"
    )
    notification_read_enabled = sgqlc.types.Field(Boolean, graphql_name="notificationReadEnabled")
    notification_write_enabled = sgqlc.types.Field(Boolean, graphql_name="notificationWriteEnabled")
    read_any_asset_enabled = sgqlc.types.Field(Boolean, graphql_name="readAnyAssetEnabled")
    room_write_enabled = sgqlc.types.Field(Boolean, graphql_name="roomWriteEnabled")
    room_write_configuration_enabled = sgqlc.types.Field(
        Boolean, graphql_name="roomWriteConfigurationEnabled"
    )


########################################################################
# Unions
########################################################################

########################################################################
# Schema Entry Points
########################################################################
api_server_schema.query_type = Query
api_server_schema.mutation_type = Mutation
api_server_schema.subscription_type = None
